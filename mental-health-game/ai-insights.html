<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Insights - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="ai-insights-page">
    <div class="container">
        <header class="page-header">
            <h1>🧠 AI-Powered Insights</h1>
            <p>Deep analysis of your mental wellness patterns and personalized recommendations</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="ai-insights.html" class="nav-link active">AI Insights</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="insights-content">
            <!-- AI Status -->
            <section class="insights-section">
                <div class="dashboard-card ai-status-card">
                    <div class="ai-status-header">
                        <h2>🤖 AI Analysis Status</h2>
                        <div class="ai-status-indicator" id="ai-status">
                            <span class="status-dot"></span>
                            <span class="status-text">Checking AI connection...</span>
                        </div>
                    </div>
                    <div class="ai-capabilities" id="ai-capabilities">
                        <div class="capability-item">
                            <span class="capability-icon">🧠</span>
                            <span class="capability-text">Emotional Pattern Analysis</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">📊</span>
                            <span class="capability-text">Behavioral Insights</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🎯</span>
                            <span class="capability-text">Personalized Recommendations</span>
                        </div>
                        <div class="capability-item">
                            <span class="capability-icon">🔮</span>
                            <span class="capability-text">Predictive Wellness</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Emotional Intelligence Analysis -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>💭 Emotional Intelligence Analysis</h2>
                    <div class="analysis-container" id="emotional-analysis">
                        <div class="analysis-loading">
                            <div class="loading-spinner"></div>
                            <p>Analyzing your emotional patterns...</p>
                        </div>
                    </div>
                    <button id="analyze-emotions" class="primary-btn">Generate Emotional Analysis</button>
                </div>
            </section>

            <!-- Behavioral Patterns -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>🔄 Behavioral Pattern Recognition</h2>
                    <div class="analysis-container" id="behavioral-analysis">
                        <div class="analysis-placeholder">
                            <p>Click "Analyze Patterns" to discover insights about your wellness behaviors and habits.</p>
                        </div>
                    </div>
                    <button id="analyze-patterns" class="primary-btn">Analyze Patterns</button>
                </div>
            </section>

            <!-- Personalized Recommendations -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>🎯 AI-Generated Recommendations</h2>
                    <div class="recommendations-grid" id="recommendations-grid">
                        <div class="recommendation-placeholder">
                            <p>Generate personalized recommendations based on your unique wellness journey.</p>
                        </div>
                    </div>
                    <button id="generate-recommendations" class="primary-btn">Generate Recommendations</button>
                </div>
            </section>

            <!-- Predictive Insights -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>🔮 Predictive Wellness Insights</h2>
                    <div class="prediction-container" id="prediction-container">
                        <div class="prediction-placeholder">
                            <p>AI will analyze your patterns to predict optimal times for activities and potential challenges.</p>
                        </div>
                    </div>
                    <button id="generate-predictions" class="primary-btn">Generate Predictions</button>
                </div>
            </section>

            <!-- Weekly AI Summary -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>📅 Weekly AI Summary</h2>
                    <div class="summary-container" id="weekly-summary">
                        <div class="summary-placeholder">
                            <p>Get a comprehensive AI-generated summary of your weekly progress and insights.</p>
                        </div>
                    </div>
                    <button id="generate-weekly-summary" class="primary-btn">Generate Weekly Summary</button>
                </div>
            </section>

            <!-- AI Chat Integration -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>💬 Ask AI About Your Data</h2>
                    <div class="ai-chat-container">
                        <div class="chat-input-group">
                            <input type="text" id="ai-question" placeholder="Ask me anything about your wellness data..." maxlength="200">
                            <button id="ask-ai" class="primary-btn">Ask AI</button>
                        </div>
                        <div class="ai-response-container" id="ai-response-container">
                            <div class="response-placeholder">
                                <p>💡 Try asking: "What patterns do you see in my mood?" or "When am I most productive?"</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Export AI Insights -->
            <section class="insights-section">
                <div class="dashboard-card">
                    <h2>📤 Export AI Insights</h2>
                    <p>Download your AI-generated insights and recommendations for external use or sharing with healthcare providers.</p>
                    <div class="export-options">
                        <button id="export-insights-pdf" class="secondary-btn">Export as PDF Report</button>
                        <button id="export-insights-json" class="secondary-btn">Export Raw Data</button>
                        <button id="export-insights-summary" class="secondary-btn">Export Summary</button>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/openai-integration.js"></script>
    <script src="js/enhanced-features.js"></script>
    <script src="js/advanced-ai.js"></script>
    <script src="js/ai-insights.js"></script>

    <style>
        .insights-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .insights-section {
            margin-bottom: 2rem;
        }

        .ai-status-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.3);
        }

        .ai-status-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .ai-status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 20px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ccc;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #56ab2f;
        }

        .status-dot.disconnected {
            background: #ff6b6b;
        }

        .ai-capabilities {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .capability-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .capability-icon {
            font-size: 1.5rem;
        }

        .capability-text {
            font-weight: 600;
            color: #333;
        }

        .analysis-container {
            min-height: 200px;
            margin: 1.5rem 0;
            padding: 2rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 2px dashed rgba(102, 126, 234, 0.2);
        }

        .analysis-loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            height: 150px;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(102, 126, 234, 0.2);
            border-left: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        .analysis-placeholder,
        .recommendation-placeholder,
        .prediction-placeholder,
        .summary-placeholder,
        .response-placeholder {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 2rem;
        }

        .insight-card {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .insight-card h4 {
            margin: 0 0 1rem 0;
            color: #333;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .insight-card p {
            margin: 0;
            color: #555;
            line-height: 1.6;
        }

        .recommendations-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .recommendation-card {
            background: linear-gradient(135deg, rgba(86, 171, 47, 0.1), rgba(76, 175, 80, 0.1));
            border: 2px solid rgba(86, 171, 47, 0.2);
            border-radius: 15px;
            padding: 1.5rem;
        }

        .recommendation-card h4 {
            margin: 0 0 1rem 0;
            color: #56ab2f;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .recommendation-card p {
            margin: 0 0 1rem 0;
            color: #333;
        }

        .recommendation-priority {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .priority-high {
            background: #ff6b6b;
            color: white;
        }

        .priority-medium {
            background: #ffc107;
            color: white;
        }

        .priority-low {
            background: #56ab2f;
            color: white;
        }

        .prediction-container {
            margin: 1.5rem 0;
        }

        .prediction-item {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .prediction-item h4 {
            margin: 0 0 0.5rem 0;
            color: #ff9800;
        }

        .prediction-confidence {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.5rem;
        }

        .ai-chat-container {
            margin: 1.5rem 0;
        }

        .chat-input-group {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }

        .chat-input-group input {
            flex: 1;
            padding: 1rem;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 1rem;
        }

        .chat-input-group input:focus {
            border-color: #667eea;
            outline: none;
        }

        .ai-response-container {
            min-height: 100px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            padding: 1.5rem;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .ai-response {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            border-left: 4px solid #667eea;
            margin: 1rem 0;
        }

        .export-options {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            justify-content: center;
            margin: 1.5rem 0;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        @media (max-width: 768px) {
            .ai-status-header {
                flex-direction: column;
                gap: 1rem;
                text-align: center;
            }
            
            .ai-capabilities {
                grid-template-columns: 1fr;
            }
            
            .recommendations-grid {
                grid-template-columns: 1fr;
            }
            
            .chat-input-group {
                flex-direction: column;
            }
            
            .export-options {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</body>
</html>
