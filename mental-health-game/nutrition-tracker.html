<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nutrition Tracker - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="nutrition-page">
    <div class="container">
        <header class="page-header">
            <h1>🥗 Nutrition & Wellness</h1>
            <p>Track your nutrition for better mental health and energy</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link active">Nutrition</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="nutrition-content">
            <!-- Daily Nutrition Log -->
            <section class="nutrition-section">
                <div class="dashboard-card">
                    <h2>🍎 Daily Nutrition Log</h2>
                    <div class="nutrition-entry-form">
                        <div class="date-selector">
                            <label for="nutrition-date">Date:</label>
                            <input type="date" id="nutrition-date">
                        </div>

                        <div class="wellness-categories">
                            <div class="category-card">
                                <h3>💧 Hydration</h3>
                                <div class="hydration-tracker">
                                    <div class="water-glasses" id="water-glasses">
                                        <!-- Generated by JavaScript -->
                                    </div>
                                    <div class="hydration-controls">
                                        <button id="add-water" class="icon-btn">➕</button>
                                        <span id="water-count">0</span> glasses
                                        <button id="remove-water" class="icon-btn">➖</button>
                                    </div>
                                </div>
                            </div>

                            <div class="category-card">
                                <h3>🥬 Vegetables & Fruits</h3>
                                <div class="serving-tracker">
                                    <div class="serving-counter">
                                        <button class="serving-btn" data-type="vegetables">🥬 Vegetables</button>
                                        <span id="veg-count">0</span> servings
                                    </div>
                                    <div class="serving-counter">
                                        <button class="serving-btn" data-type="fruits">🍎 Fruits</button>
                                        <span id="fruit-count">0</span> servings
                                    </div>
                                </div>
                            </div>

                            <div class="category-card">
                                <h3>🍞 Whole Grains</h3>
                                <div class="serving-counter">
                                    <button class="serving-btn" data-type="grains">🍞 Add Serving</button>
                                    <span id="grain-count">0</span> servings
                                </div>
                            </div>

                            <div class="category-card">
                                <h3>🥜 Protein</h3>
                                <div class="serving-counter">
                                    <button class="serving-btn" data-type="protein">🥜 Add Serving</button>
                                    <span id="protein-count">0</span> servings
                                </div>
                            </div>
                        </div>

                        <div class="mood-food-connection">
                            <h3>🧠 Mood & Food Connection</h3>
                            <div class="mood-food-grid">
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="caffeine">
                                    <span>☕ Caffeine</span>
                                </label>
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="sugar">
                                    <span>🍭 High Sugar</span>
                                </label>
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="processed">
                                    <span>🍟 Processed Foods</span>
                                </label>
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="omega3">
                                    <span>🐟 Omega-3 Rich</span>
                                </label>
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="probiotics">
                                    <span>🥛 Probiotics</span>
                                </label>
                                <label class="mood-food-item">
                                    <input type="checkbox" name="mood-foods" value="antioxidants">
                                    <span>🫐 Antioxidants</span>
                                </label>
                            </div>
                        </div>

                        <div class="energy-rating">
                            <h3>⚡ Energy Level</h3>
                            <div class="energy-selector">
                                <div class="energy-option" data-energy="1">
                                    <div class="energy-emoji">😴</div>
                                    <span>Very Low</span>
                                </div>
                                <div class="energy-option" data-energy="2">
                                    <div class="energy-emoji">😐</div>
                                    <span>Low</span>
                                </div>
                                <div class="energy-option" data-energy="3">
                                    <div class="energy-emoji">🙂</div>
                                    <span>Moderate</span>
                                </div>
                                <div class="energy-option" data-energy="4">
                                    <div class="energy-emoji">😊</div>
                                    <span>High</span>
                                </div>
                                <div class="energy-option" data-energy="5">
                                    <div class="energy-emoji">🤩</div>
                                    <span>Very High</span>
                                </div>
                            </div>
                        </div>

                        <div class="nutrition-notes">
                            <label for="nutrition-notes">Notes (Optional):</label>
                            <textarea id="nutrition-notes" placeholder="How did your food choices affect your mood and energy today?" maxlength="200"></textarea>
                        </div>

                        <button id="save-nutrition" class="primary-btn">Save Daily Nutrition</button>
                    </div>
                </div>
            </section>

            <!-- Nutrition Statistics -->
            <section class="nutrition-section">
                <div class="dashboard-card">
                    <h2>📊 Nutrition Statistics</h2>
                    <div class="nutrition-stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">💧</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-water">0</span>
                                <span class="stat-label">Avg Water/Day</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🥬</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-vegetables">0</span>
                                <span class="stat-label">Avg Vegetables/Day</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⚡</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-energy">0.0</span>
                                <span class="stat-label">Avg Energy Level</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📅</div>
                            <div class="stat-info">
                                <span class="stat-number" id="nutrition-streak">0</span>
                                <span class="stat-label">Tracking Streak</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Nutrition Analysis -->
            <section class="nutrition-section">
                <div class="dashboard-card ai-nutrition-card">
                    <h2>🤖 AI Nutrition Analysis</h2>
                    <div class="ai-analysis-container" id="nutrition-ai-analysis">
                        <div class="analysis-placeholder">
                            <p>AI will analyze your nutrition patterns and their impact on your mood and energy levels.</p>
                        </div>
                    </div>
                    <button id="analyze-nutrition" class="primary-btn">Generate Nutrition Analysis</button>
                </div>
            </section>

            <!-- Nutrition Recommendations -->
            <section class="nutrition-section">
                <div class="dashboard-card">
                    <h2>💡 Nutrition for Mental Health</h2>
                    <div class="nutrition-tips-grid">
                        <div class="tip-card">
                            <h4>🧠 Brain Foods</h4>
                            <p>Omega-3 rich foods like salmon, walnuts, and flaxseeds support brain health and mood regulation.</p>
                        </div>
                        <div class="tip-card">
                            <h4>💧 Stay Hydrated</h4>
                            <p>Even mild dehydration can affect mood and cognitive function. Aim for 8 glasses of water daily.</p>
                        </div>
                        <div class="tip-card">
                            <h4>🍎 Stable Blood Sugar</h4>
                            <p>Eat regular meals with complex carbs and protein to maintain steady energy and mood.</p>
                        </div>
                        <div class="tip-card">
                            <h4>🥛 Gut Health</h4>
                            <p>Probiotics and fiber support gut health, which is closely linked to mental wellbeing.</p>
                        </div>
                        <div class="tip-card">
                            <h4>🫐 Antioxidants</h4>
                            <p>Colorful fruits and vegetables provide antioxidants that protect against stress and inflammation.</p>
                        </div>
                        <div class="tip-card">
                            <h4>⚖️ Balance</h4>
                            <p>Focus on overall patterns rather than perfection. Small, consistent changes make a big difference.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Nutrition History -->
            <section class="nutrition-section">
                <div class="dashboard-card">
                    <h2>📅 Nutrition History</h2>
                    <div class="nutrition-history" id="nutrition-history">
                        <div class="no-entries">
                            <p>No nutrition entries yet. Start tracking your nutrition to see your history!</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/openai-integration.js"></script>
    <script src="js/enhanced-features.js"></script>
    <script src="js/advanced-ai.js"></script>
    <script src="js/nutrition-tracker.js"></script>

    <style>
        .nutrition-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .nutrition-section {
            margin-bottom: 2rem;
        }

        .nutrition-entry-form {
            padding: 1.5rem 0;
        }

        .date-selector {
            margin-bottom: 2rem;
        }

        .wellness-categories {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .category-card {
            padding: 1.5rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .category-card h3 {
            margin: 0 0 1rem 0;
            color: #333;
            text-align: center;
        }

        .hydration-tracker {
            text-align: center;
        }

        .water-glasses {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 0.5rem;
            margin: 1rem 0;
        }

        .water-glass {
            width: 30px;
            height: 40px;
            border: 2px solid #667eea;
            border-radius: 0 0 15px 15px;
            position: relative;
            transition: all 0.3s ease;
        }

        .water-glass.filled {
            background: linear-gradient(to top, #4fc3f7 0%, #29b6f6 100%);
        }

        .hydration-controls {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 1rem;
            margin-top: 1rem;
        }

        .icon-btn {
            width: 30px;
            height: 30px;
            border: none;
            border-radius: 50%;
            background: #667eea;
            color: white;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .icon-btn:hover {
            background: #5a6fd8;
            transform: scale(1.1);
        }

        .serving-tracker {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .serving-counter {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 1rem;
        }

        .serving-btn {
            background: #56ab2f;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            flex: 1;
        }

        .serving-btn:hover {
            background: #4a9625;
            transform: translateY(-1px);
        }

        .mood-food-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .mood-food-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mood-food-item:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .mood-food-item input:checked + span {
            font-weight: 600;
            color: #667eea;
        }

        .energy-selector {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .energy-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: rgba(255, 193, 7, 0.05);
        }

        .energy-option:hover {
            background: rgba(255, 193, 7, 0.1);
        }

        .energy-option.selected {
            border-color: #ffc107;
            background: rgba(255, 193, 7, 0.15);
        }

        .energy-emoji {
            font-size: 2rem;
        }

        .nutrition-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .ai-nutrition-card {
            background: linear-gradient(135deg, rgba(86, 171, 47, 0.1), rgba(76, 175, 80, 0.1));
            border: 2px solid rgba(86, 171, 47, 0.3);
        }

        .nutrition-tips-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .nutrition-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #56ab2f;
        }

        .nutrition-entry-info {
            flex: 1;
        }

        .nutrition-entry-date {
            font-weight: 600;
            color: #333;
        }

        .nutrition-entry-summary {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .nutrition-entry-energy {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .wellness-categories {
                grid-template-columns: 1fr;
            }
            
            .energy-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .mood-food-grid {
                grid-template-columns: 1fr;
            }
            
            .nutrition-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .nutrition-tips-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
