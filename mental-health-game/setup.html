<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - MindJourney Enhanced</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="setup-page">
    <div class="container">
        <header class="page-header">
            <h1>🚀 MindJourney Enhanced Setup</h1>
            <p>Complete setup guide for your enhanced mental wellness companion</p>
        </header>

        <main class="setup-content">
            <!-- Step 1: Basic Setup -->
            <section class="setup-section">
                <div class="dashboard-card">
                    <h2>📋 Step 1: Basic Application</h2>
                    <div class="setup-step completed" id="basic-setup">
                        <div class="step-icon">✅</div>
                        <div class="step-content">
                            <h3>Application Ready</h3>
                            <p>Your MindJourney application is installed and ready to use with all core features.</p>
                            <div class="step-actions">
                                <a href="index.html" class="primary-btn">Start Basic App</a>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 2: OpenAI Configuration -->
            <section class="setup-section">
                <div class="dashboard-card">
                    <h2>🤖 Step 2: AI Enhancement (Optional)</h2>
                    <div class="setup-step" id="ai-setup">
                        <div class="step-icon" id="ai-icon">⚙️</div>
                        <div class="step-content">
                            <h3>Configure AI Features</h3>
                            <p>Enable dynamic, personalized responses with OpenAI integration.</p>
                            
                            <div class="ai-setup-form">
                                <div class="info-box">
                                    <h4>🔑 Get Your OpenAI API Key:</h4>
                                    <ol>
                                        <li>Visit <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a></li>
                                        <li>Sign in or create an account</li>
                                        <li>Click "Create new secret key"</li>
                                        <li>Copy your key (starts with sk-proj- or sk-)</li>
                                        <li>Paste it below</li>
                                    </ol>
                                </div>

                                <div class="input-group">
                                    <label for="setup-api-key">OpenAI API Key:</label>
                                    <input type="password" id="setup-api-key" placeholder="sk-proj-..." maxlength="200">
                                    <small class="input-help">Your key is stored locally and never shared</small>
                                </div>

                                <div class="step-actions">
                                    <button id="test-ai-setup" class="primary-btn">Test & Save</button>
                                    <button id="skip-ai-setup" class="secondary-btn">Skip for Now</button>
                                </div>

                                <div id="ai-test-result" class="test-result hidden"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 3: Feature Overview -->
            <section class="setup-section">
                <div class="dashboard-card">
                    <h2>🌟 Step 3: Explore Features</h2>
                    <div class="features-grid">
                        <div class="feature-card">
                            <div class="feature-icon">🎯</div>
                            <h4>Daily Tasks</h4>
                            <p>Breathing, journaling, mood tracking, and meditation</p>
                            <a href="tasks.html" class="feature-link">Try Tasks</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🏆</div>
                            <h4>Rewards & Badges</h4>
                            <p>Earn XP, unlock badges, and track your progress</p>
                            <a href="rewards.html" class="feature-link">View Rewards</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🤖</div>
                            <h4>AI Companion</h4>
                            <p>Chat with your supportive AI wellness friend</p>
                            <a href="bot.html" class="feature-link">Meet Your AI</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">📊</div>
                            <h4>Analytics</h4>
                            <p>Detailed insights into your wellness patterns</p>
                            <a href="analytics.html" class="feature-link">View Analytics</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">🎯</div>
                            <h4>Goal Setting</h4>
                            <p>Set and track personalized wellness objectives</p>
                            <a href="goals.html" class="feature-link">Set Goals</a>
                        </div>

                        <div class="feature-card">
                            <div class="feature-icon">⚙️</div>
                            <h4>Settings</h4>
                            <p>Customize your experience and manage data</p>
                            <a href="settings.html" class="feature-link">Open Settings</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Step 4: Quick Start -->
            <section class="setup-section">
                <div class="dashboard-card">
                    <h2>🚀 Step 4: Quick Start Guide</h2>
                    <div class="quick-start">
                        <div class="start-option">
                            <h4>🆕 New User</h4>
                            <p>First time using MindJourney? Start here to create your profile and learn the basics.</p>
                            <a href="index.html" class="primary-btn">Create Profile</a>
                        </div>

                        <div class="start-option">
                            <h4>🔄 Returning User</h4>
                            <p>Welcome back! Continue your wellness journey from where you left off.</p>
                            <a href="dashboard.html" class="primary-btn">Go to Dashboard</a>
                        </div>

                        <div class="start-option">
                            <h4>📖 Learn More</h4>
                            <p>Want to understand all features? Read the comprehensive guide.</p>
                            <a href="SETUP-GUIDE.md" target="_blank" class="secondary-btn">Read Guide</a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Cost Information -->
            <section class="setup-section">
                <div class="dashboard-card cost-info-card">
                    <h2>💰 AI Features Cost Information</h2>
                    <div class="cost-breakdown">
                        <div class="cost-item">
                            <h4>Daily Usage</h4>
                            <span class="cost-amount">$0.01 - $0.03</span>
                            <p>5-15 AI interactions per day</p>
                        </div>
                        <div class="cost-item">
                            <h4>Monthly Estimate</h4>
                            <span class="cost-amount">$0.50 - $2.00</span>
                            <p>Regular daily usage</p>
                        </div>
                        <div class="cost-item">
                            <h4>Heavy Usage</h4>
                            <span class="cost-amount">$3.00 - $8.00</span>
                            <p>Extensive conversations and features</p>
                        </div>
                    </div>
                    <p class="cost-note">
                        💡 <strong>Tip:</strong> Monitor your usage at <a href="https://platform.openai.com/usage" target="_blank">OpenAI Usage Dashboard</a>
                    </p>
                </div>
            </section>

            <!-- Support -->
            <section class="setup-section">
                <div class="dashboard-card">
                    <h2>🆘 Need Help?</h2>
                    <div class="support-options">
                        <div class="support-item">
                            <h4>📖 Documentation</h4>
                            <p>Comprehensive guides and troubleshooting</p>
                            <a href="SETUP-GUIDE.md" target="_blank" class="support-link">Read Docs</a>
                        </div>
                        <div class="support-item">
                            <h4>🔧 Troubleshooting</h4>
                            <p>Common issues and solutions</p>
                            <button onclick="showTroubleshooting()" class="support-link">View Issues</button>
                        </div>
                        <div class="support-item">
                            <h4>🔒 Privacy Info</h4>
                            <p>How your data is protected</p>
                            <button onclick="showPrivacyInfo()" class="support-link">Privacy Policy</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/openai-integration.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkSetupStatus();
            setupEventListeners();
        });

        function checkSetupStatus() {
            // Check if AI is already configured
            if (openAI && openAI.initialize()) {
                updateAISetupStatus(true);
            }
        }

        function setupEventListeners() {
            document.getElementById('test-ai-setup').addEventListener('click', testAISetup);
            document.getElementById('skip-ai-setup').addEventListener('click', skipAISetup);
        }

        async function testAISetup() {
            const apiKey = document.getElementById('setup-api-key').value.trim();
            const resultDiv = document.getElementById('ai-test-result');
            const testBtn = document.getElementById('test-ai-setup');

            if (!apiKey) {
                showTestResult('Please enter an API key', 'error');
                return;
            }

            if (!apiKey.startsWith('sk-')) {
                showTestResult('Invalid API key format. Keys should start with "sk-"', 'error');
                return;
            }

            testBtn.textContent = 'Testing...';
            testBtn.disabled = true;
            showTestResult('Testing connection...', 'loading');

            try {
                const success = openAI.setApiKey(apiKey);
                if (success) {
                    const connectionTest = await openAI.testConnection();
                    if (connectionTest) {
                        showTestResult('✅ Success! AI features are now enabled.', 'success');
                        updateAISetupStatus(true);
                        document.getElementById('setup-api-key').value = '';
                    } else {
                        showTestResult('❌ Connection failed. Please check your API key.', 'error');
                        openAI.removeApiKey();
                    }
                } else {
                    showTestResult('❌ Invalid API key format.', 'error');
                }
            } catch (error) {
                showTestResult(`❌ Error: ${error.message}`, 'error');
                openAI.removeApiKey();
            }

            testBtn.textContent = 'Test & Save';
            testBtn.disabled = false;
        }

        function skipAISetup() {
            showTestResult('ℹ️ AI features skipped. You can configure them later in Settings.', 'info');
            setTimeout(() => {
                document.getElementById('ai-test-result').classList.add('hidden');
            }, 3000);
        }

        function updateAISetupStatus(enabled) {
            const aiStep = document.getElementById('ai-setup');
            const aiIcon = document.getElementById('ai-icon');

            if (enabled) {
                aiStep.classList.add('completed');
                aiIcon.textContent = '✅';
            } else {
                aiStep.classList.remove('completed');
                aiIcon.textContent = '⚙️';
            }
        }

        function showTestResult(message, type) {
            const resultDiv = document.getElementById('ai-test-result');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultDiv.classList.remove('hidden');

            if (type !== 'loading') {
                setTimeout(() => {
                    resultDiv.classList.add('hidden');
                }, 5000);
            }
        }

        function showTroubleshooting() {
            alert(`Common Issues & Solutions:

🔧 AI Features Not Working:
- Check your API key is valid
- Ensure you have internet connection
- Verify you have OpenAI credits

🔧 App Running Slowly:
- Close other browser tabs
- Clear browser cache
- Restart browser

🔧 Data Not Saving:
- Check localStorage is enabled
- Don't use incognito/private mode
- Try exporting/importing data

🔧 Can't See Files:
- Check you're in the right directory
- Refresh file manager
- Look for hidden files

Need more help? Check the SETUP-GUIDE.md file.`);
        }

        function showPrivacyInfo() {
            alert(`🔒 Privacy & Security:

✅ Local Storage Only:
- All personal data stays on your device
- No external tracking or analytics
- You control your data completely

✅ AI Interactions:
- Only current conversation sent to OpenAI
- No personal identifying information shared
- Conversations not stored permanently

✅ API Key Security:
- Stored locally in your browser only
- Never shared with external services
- You can remove it anytime

✅ Data Control:
- Export your data anytime
- Import/restore from backups
- Delete all data with one click

Your privacy and security are our top priorities.`);
        }
    </script>

    <style>
        .setup-content {
            max-width: 1000px;
            margin: 0 auto;
        }

        .setup-section {
            margin-bottom: 2rem;
        }

        .setup-step {
            display: flex;
            gap: 1.5rem;
            padding: 2rem;
            border-radius: 15px;
            border: 2px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .setup-step.completed {
            border-color: #56ab2f;
            background: rgba(86, 171, 47, 0.05);
        }

        .step-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
            flex-shrink: 0;
        }

        .setup-step.completed .step-icon {
            background: rgba(86, 171, 47, 0.1);
        }

        .step-content {
            flex: 1;
        }

        .step-content h3 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .step-content p {
            margin: 0 0 1.5rem 0;
            color: #666;
        }

        .step-actions {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .info-box {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.2);
            border-radius: 10px;
            padding: 1.5rem;
            margin: 1rem 0;
        }

        .info-box h4 {
            margin: 0 0 1rem 0;
            color: #667eea;
        }

        .info-box ol {
            margin: 0;
            padding-left: 1.5rem;
        }

        .info-box li {
            margin-bottom: 0.5rem;
        }

        .test-result {
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            font-weight: 600;
        }

        .test-result.success {
            background: rgba(86, 171, 47, 0.1);
            color: #56ab2f;
            border: 1px solid rgba(86, 171, 47, 0.3);
        }

        .test-result.error {
            background: rgba(255, 107, 107, 0.1);
            color: #ff6b6b;
            border: 1px solid rgba(255, 107, 107, 0.3);
        }

        .test-result.info {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 1px solid rgba(102, 126, 234, 0.3);
        }

        .test-result.loading {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .feature-card {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .feature-card h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .feature-card p {
            margin: 0 0 1rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .feature-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border: 1px solid #667eea;
            border-radius: 20px;
            transition: all 0.3s ease;
        }

        .feature-link:hover {
            background: #667eea;
            color: white;
        }

        .quick-start {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin: 1.5rem 0;
        }

        .start-option {
            text-align: center;
            padding: 2rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .start-option h4 {
            margin: 0 0 1rem 0;
            color: #333;
        }

        .start-option p {
            margin: 0 0 1.5rem 0;
            color: #666;
        }

        .cost-info-card {
            background: linear-gradient(135deg, rgba(255, 193, 7, 0.1), rgba(255, 152, 0, 0.1));
            border: 2px solid rgba(255, 193, 7, 0.3);
        }

        .cost-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .cost-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
        }

        .cost-item h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .cost-amount {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #ff9800;
            margin-bottom: 0.5rem;
        }

        .cost-item p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .cost-note {
            background: rgba(255, 193, 7, 0.1);
            padding: 1rem;
            border-radius: 10px;
            border-left: 4px solid #ffc107;
            margin: 1rem 0;
        }

        .support-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .support-item {
            text-align: center;
            padding: 1.5rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
        }

        .support-item h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .support-item p {
            margin: 0 0 1rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .support-link {
            color: #667eea;
            text-decoration: none;
            font-weight: 600;
            padding: 0.5rem 1rem;
            border: 1px solid #667eea;
            border-radius: 20px;
            background: transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .support-link:hover {
            background: #667eea;
            color: white;
        }

        @media (max-width: 768px) {
            .setup-step {
                flex-direction: column;
                text-align: center;
            }
            
            .features-grid, .quick-start, .cost-breakdown, .support-options {
                grid-template-columns: 1fr;
            }
            
            .step-actions {
                justify-content: center;
            }
        }
    </style>
</body>
</html>
