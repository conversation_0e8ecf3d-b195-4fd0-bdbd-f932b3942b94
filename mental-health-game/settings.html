<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="settings-page">
    <div class="container">
        <header class="dashboard-header">
            <div class="user-info">
                <div id="user-avatar" class="avatar-circle"></div>
                <div class="user-details">
                    <h2>Settings</h2>
                    <p class="page-subtitle">Customize your experience</p>
                </div>
            </div>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="settings.html" class="nav-link active">Settings</a>
            </nav>
        </header>

        <main class="settings-content">
            <!-- Profile Settings -->
            <section class="profile-settings">
                <div class="dashboard-card">
                    <h3>Profile Settings</h3>
                    <form id="profile-form" class="settings-form">
                        <div class="form-group">
                            <label for="display-name">Display Name</label>
                            <input type="text" id="display-name" name="displayName" placeholder="Your display name">
                        </div>
                        
                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" id="email" name="email" placeholder="<EMAIL>" readonly>
                            <small>Email cannot be changed</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="avatar-select">Avatar</label>
                            <div class="avatar-selection">
                                <div class="avatar-grid">
                                    <div class="avatar-option" data-avatar="peaceful">
                                        <div class="avatar-circle peaceful-avatar">🧘</div>
                                    </div>
                                    <div class="avatar-option" data-avatar="nature">
                                        <div class="avatar-circle nature-avatar">🌱</div>
                                    </div>
                                    <div class="avatar-option" data-avatar="strong">
                                        <div class="avatar-circle strong-avatar">💪</div>
                                    </div>
                                    <div class="avatar-option" data-avatar="wise">
                                        <div class="avatar-circle wise-avatar">🦉</div>
                                    </div>
                                    <div class="avatar-option" data-avatar="creative">
                                        <div class="avatar-circle creative-avatar">🎨</div>
                                    </div>
                                    <div class="avatar-option" data-avatar="bright">
                                        <div class="avatar-circle bright-avatar">⭐</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <button type="submit" class="primary-btn">Save Profile</button>
                    </form>
                </div>
            </section>

            <!-- Notification Settings -->
            <section class="notification-settings">
                <div class="dashboard-card">
                    <h3>Notifications</h3>
                    <div class="settings-form">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Daily Reminders</h4>
                                <p>Get reminded to complete your daily wellness tasks</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="daily-reminders" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Achievement Notifications</h4>
                                <p>Celebrate when you unlock new badges and rewards</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="achievement-notifications" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Weekly Reports</h4>
                                <p>Receive weekly progress summaries</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="weekly-reports" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Privacy Settings -->
            <section class="privacy-settings">
                <div class="dashboard-card">
                    <h3>Privacy & Data</h3>
                    <div class="settings-form">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Data Analytics</h4>
                                <p>Help improve the app by sharing anonymous usage data</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="data-analytics">
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>AI Learning</h4>
                                <p>Allow AI companion to learn from your interactions</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="ai-learning" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- App Settings -->
            <section class="app-settings">
                <div class="dashboard-card">
                    <h3>App Preferences</h3>
                    <div class="settings-form">
                        <div class="form-group">
                            <label for="theme-select">Theme</label>
                            <select id="theme-select" name="theme">
                                <option value="light">Light</option>
                                <option value="dark">Dark</option>
                                <option value="auto">Auto (System)</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="language-select">Language</label>
                            <select id="language-select" name="language">
                                <option value="en">English</option>
                                <option value="es">Español</option>
                                <option value="fr">Français</option>
                                <option value="de">Deutsch</option>
                            </select>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Sound Effects</h4>
                                <p>Play sounds for interactions and achievements</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="sound-effects" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h4>Animations</h4>
                                <p>Enable smooth animations and transitions</p>
                            </div>
                            <label class="toggle-switch">
                                <input type="checkbox" id="animations" checked>
                                <span class="slider"></span>
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Data Management -->
            <section class="data-management">
                <div class="dashboard-card">
                    <h3>Data Management</h3>
                    <div class="data-actions">
                        <button class="secondary-btn" id="export-data">
                            <span class="btn-icon">📥</span>
                            Export My Data
                        </button>
                        
                        <button class="secondary-btn" id="import-data">
                            <span class="btn-icon">📤</span>
                            Import Data
                        </button>
                        
                        <button class="danger-btn" id="reset-data">
                            <span class="btn-icon">🔄</span>
                            Reset All Data
                        </button>
                        
                        <button class="danger-btn" id="delete-account">
                            <span class="btn-icon">🗑️</span>
                            Delete Account
                        </button>
                    </div>
                </div>
            </section>

            <!-- Account Actions -->
            <section class="account-actions">
                <div class="dashboard-card">
                    <h3>Account</h3>
                    <div class="account-info">
                        <p><strong>Account Type:</strong> <span id="account-type">Free</span></p>
                        <p><strong>Member Since:</strong> <span id="member-since">-</span></p>
                        <p><strong>Last Login:</strong> <span id="last-login">-</span></p>
                    </div>
                    
                    <div class="account-actions-buttons">
                        <button class="secondary-btn" id="change-password">Change Password</button>
                        <button class="danger-btn" id="sign-out">Sign Out</button>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Confirmation Modal -->
    <div id="confirmation-modal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-title">Confirm Action</h3>
                <button class="modal-close" id="close-modal">&times;</button>
            </div>
            <div class="modal-body">
                <p id="modal-message">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button class="secondary-btn" id="modal-cancel">Cancel</button>
                <button class="danger-btn" id="modal-confirm">Confirm</button>
            </div>
        </div>
    </div>

    <!-- Success Message -->
    <div id="success-message" class="success-message hidden">
        <div class="message-content">
            <span class="message-icon">✅</span>
            <span class="message-text">Settings saved successfully!</span>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize settings page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            const currentUser = Auth.getCurrentUser();
            if (!currentUser) {
                window.location.href = 'signin.html';
                return;
            }

            initializeSettings();
        });

        function initializeSettings() {
            loadUserData();
            loadSettings();
            setupEventListeners();
        }

        function loadUserData() {
            const userData = getUserData();
            const currentUser = Auth.getCurrentUser();
            
            if (currentUser && userData) {
                // Update avatar display
                document.getElementById('user-avatar').textContent = currentUser.avatar || '🧘';
                document.getElementById('user-avatar').className = `avatar-circle ${userData.avatar}-avatar`;
                
                // Load profile data
                document.getElementById('display-name').value = userData.username || currentUser.fullName || '';
                document.getElementById('email').value = currentUser.email || '';
                
                // Select current avatar
                const currentAvatar = userData.avatar || 'peaceful';
                document.querySelector(`[data-avatar="${currentAvatar}"]`).classList.add('selected');
                
                // Load account info
                document.getElementById('account-type').textContent = currentUser.isDemo ? 'Demo' : 'Free';
                document.getElementById('member-since').textContent = new Date(currentUser.createdAt).toLocaleDateString();
                document.getElementById('last-login').textContent = currentUser.lastLogin ? 
                    new Date(currentUser.lastLogin).toLocaleDateString() : 'Today';
            }
        }

        function loadSettings() {
            // Load saved settings from localStorage
            const settings = JSON.parse(localStorage.getItem('mindjourney_settings') || '{}');
            
            // Apply settings to form elements
            Object.keys(settings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = settings[key];
                    } else {
                        element.value = settings[key];
                    }
                }
            });
        }

        function setupEventListeners() {
            // Profile form
            document.getElementById('profile-form').addEventListener('submit', handleProfileUpdate);
            
            // Avatar selection
            document.querySelectorAll('.avatar-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.avatar-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });
            
            // Settings toggles
            document.querySelectorAll('input[type="checkbox"], select').forEach(input => {
                input.addEventListener('change', saveSettings);
            });
            
            // Data management buttons
            document.getElementById('export-data').addEventListener('click', exportData);
            document.getElementById('import-data').addEventListener('click', importData);
            document.getElementById('reset-data').addEventListener('click', () => showConfirmation('Reset Data', 'This will delete all your progress. Are you sure?', resetData));
            document.getElementById('delete-account').addEventListener('click', () => showConfirmation('Delete Account', 'This will permanently delete your account. Are you sure?', deleteAccount));
            
            // Account actions
            document.getElementById('change-password').addEventListener('click', changePassword);
            document.getElementById('sign-out').addEventListener('click', signOut);
            
            // Modal handlers
            document.getElementById('close-modal').addEventListener('click', hideConfirmation);
            document.getElementById('modal-cancel').addEventListener('click', hideConfirmation);
            document.getElementById('modal-confirm').addEventListener('click', executeConfirmAction);
        }

        function handleProfileUpdate(e) {
            e.preventDefault();
            
            const displayName = document.getElementById('display-name').value;
            const selectedAvatar = document.querySelector('.avatar-option.selected');
            
            if (displayName && selectedAvatar) {
                const avatarType = selectedAvatar.dataset.avatar;
                
                // Update user data
                const userData = getUserData();
                userData.username = displayName;
                userData.avatar = avatarType;
                saveUserData(userData);
                
                // Update auth user
                Auth.updateUserProfile({ fullName: displayName, avatar: getAvatarEmoji(avatarType) });
                
                showSuccessMessage('Profile updated successfully!');
            }
        }

        function saveSettings() {
            const settings = {};
            
            // Collect all settings
            document.querySelectorAll('input[type="checkbox"], select').forEach(input => {
                if (input.id) {
                    settings[input.id] = input.type === 'checkbox' ? input.checked : input.value;
                }
            });
            
            // Save to localStorage
            localStorage.setItem('mindjourney_settings', JSON.stringify(settings));
            
            // Apply theme if changed
            if (settings['theme-select']) {
                applyTheme(settings['theme-select']);
            }
        }

        function applyTheme(theme) {
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            if (theme !== 'light') {
                document.body.classList.add(`theme-${theme}`);
            }
        }

        function exportData() {
            const exportData = Auth.exportUserData();
            if (exportData) {
                const dataStr = JSON.stringify(exportData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `mindjourney-data-${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                
                showSuccessMessage('Data exported successfully!');
            }
        }

        function importData() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const importedData = JSON.parse(e.target.result);
                            // Here you would validate and import the data
                            showSuccessMessage('Data imported successfully!');
                        } catch (error) {
                            alert('Invalid file format. Please select a valid MindJourney data file.');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            
            input.click();
        }

        function resetData() {
            clearUserData();
            showSuccessMessage('All data has been reset!');
            setTimeout(() => {
                window.location.href = 'onboarding.html';
            }, 2000);
        }

        function deleteAccount() {
            Auth.deleteAccount().then(result => {
                if (result.success) {
                    alert('Account deleted successfully.');
                    window.location.href = 'signin.html';
                } else {
                    alert('Failed to delete account. Please try again.');
                }
            });
        }

        function changePassword() {
            // For demo purposes, just show a message
            alert('Password change functionality would be implemented here.');
        }

        function signOut() {
            Auth.signOut();
            window.location.href = 'signin.html';
        }

        let confirmAction = null;

        function showConfirmation(title, message, action) {
            document.getElementById('modal-title').textContent = title;
            document.getElementById('modal-message').textContent = message;
            document.getElementById('confirmation-modal').classList.remove('hidden');
            confirmAction = action;
        }

        function hideConfirmation() {
            document.getElementById('confirmation-modal').classList.add('hidden');
            confirmAction = null;
        }

        function executeConfirmAction() {
            if (confirmAction) {
                confirmAction();
                hideConfirmation();
            }
        }

        function showSuccessMessage(message) {
            const messageEl = document.getElementById('success-message');
            messageEl.querySelector('.message-text').textContent = message;
            messageEl.classList.remove('hidden');
            
            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 3000);
        }

        function getAvatarEmoji(avatarType) {
            const avatars = {
                peaceful: '🧘',
                nature: '🌱',
                strong: '💪',
                wise: '🦉',
                creative: '🎨',
                bright: '⭐'
            };
            return avatars[avatarType] || '🧘';
        }
    </script>
</body>
</html>
