<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Daily Tasks - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="tasks-page">
    <div class="container task-container">
        <header class="page-header">
            <h1>Daily Wellness Tasks</h1>
            <p>Complete these activities to nurture your mental health</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link active">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <nav class="task-navigation">
            <button class="task-nav-btn active" data-task="breathing">🌬️ Breathing</button>
            <button class="task-nav-btn" data-task="journal">📝 Journal</button>
            <button class="task-nav-btn" data-task="mood">📊 Mood</button>
            <button class="task-nav-btn" data-task="meditation">🧘 Meditation</button>
        </nav>

        <main class="tasks-content">
            <!-- Breathing Exercise Section -->
            <section id="breathing-section" class="task-section">
                <div class="task-card">
                    <h2>🌬️ Breathing Exercise</h2>
                    <p>Take a moment to center yourself with this calming breathing exercise. Follow the rhythm and let your mind find peace.</p>
                    
                    <div class="breathing-container">
                        <div class="breathing-instructions">
                            <p>Click "Start" and follow the breathing circle:</p>
                            <ul>
                                <li><strong>Inhale</strong> for 4 seconds as the circle grows</li>
                                <li><strong>Hold</strong> for 4 seconds</li>
                                <li><strong>Exhale</strong> for 6 seconds as the circle shrinks</li>
                                <li>Repeat for 5 cycles</li>
                            </ul>
                        </div>

                        <div class="breathing-circle" id="breathing-circle">
                            Ready to Begin
                        </div>
                        
                        <div class="breathing-instruction" id="breathing-instruction">
                            Click Start to begin your breathing exercise
                        </div>

                        <div class="task-controls">
                            <button id="start-breathing" class="primary-btn">Start Breathing Exercise</button>
                            <button id="stop-breathing" class="secondary-btn hidden">Stop</button>
                        </div>

                        <div id="breathing-complete" class="task-complete-indicator hidden">
                            ✅ Breathing exercise completed! +10 XP
                        </div>
                    </div>
                </div>
            </section>

            <!-- Journal Section -->
            <section id="journal-section" class="task-section hidden">
                <div class="task-card">
                    <h2>📝 Daily Journal</h2>
                    <p>Take a few minutes to reflect on your thoughts and feelings. Writing can help you process emotions and gain clarity.</p>
                    
                    <div class="journal-container">
                        <div class="input-group">
                            <label for="journal-prompt">Today's Prompt:</label>
                            <p id="journal-prompt" class="journal-prompt">What made you smile today?</p>
                        </div>

                        <div class="input-group">
                            <label for="journal-entry">Your Thoughts:</label>
                            <textarea id="journal-entry" placeholder="Write your thoughts here... There's no right or wrong way to journal. Just let your thoughts flow."></textarea>
                        </div>

                        <div class="task-controls">
                            <button id="save-journal" class="primary-btn" disabled>Save Journal Entry</button>
                            <button id="new-prompt" class="secondary-btn">Get New Prompt</button>
                        </div>

                        <div id="journal-complete" class="task-complete-indicator hidden">
                            ✅ Journal entry saved! +15 XP
                        </div>
                    </div>
                </div>
            </section>

            <!-- Mood Tracker Section -->
            <section id="mood-section" class="task-section hidden">
                <div class="task-card">
                    <h2>📊 Mood Tracker</h2>
                    <p>How are you feeling right now? Tracking your mood helps you understand patterns and celebrate progress.</p>
                    
                    <div class="mood-tracker">
                        <h3>Select your current mood:</h3>
                        
                        <div class="mood-grid">
                            <div class="mood-item" data-mood="0">
                                <div class="mood-emoji">😞</div>
                                <div class="mood-label">Very Sad</div>
                            </div>
                            <div class="mood-item" data-mood="1">
                                <div class="mood-emoji">😐</div>
                                <div class="mood-label">Sad</div>
                            </div>
                            <div class="mood-item" data-mood="2">
                                <div class="mood-emoji">🙂</div>
                                <div class="mood-label">Okay</div>
                            </div>
                            <div class="mood-item" data-mood="3">
                                <div class="mood-emoji">😃</div>
                                <div class="mood-label">Happy</div>
                            </div>
                            <div class="mood-item" data-mood="4">
                                <div class="mood-emoji">🤩</div>
                                <div class="mood-label">Very Happy</div>
                            </div>
                        </div>

                        <div id="mood-message" class="mood-message hidden">
                            <p id="mood-response"></p>
                        </div>

                        <div class="task-controls">
                            <button id="save-mood" class="primary-btn" disabled>Save Mood</button>
                        </div>

                        <div id="mood-complete" class="task-complete-indicator hidden">
                            ✅ Mood tracked! +5 XP
                        </div>
                    </div>
                </div>
            </section>

            <!-- Meditation Section -->
            <section id="meditation-section" class="task-section hidden">
                <div class="task-card">
                    <h2>🧘 Meditation Timer</h2>
                    <p>Find a comfortable position and take 2 minutes to be present. Focus on your breath and let thoughts pass by like clouds.</p>
                    
                    <div class="meditation-container">
                        <div class="meditation-instructions">
                            <h3>Meditation Guide:</h3>
                            <ul>
                                <li>Sit comfortably with your back straight</li>
                                <li>Close your eyes or soften your gaze</li>
                                <li>Focus on your natural breathing</li>
                                <li>When thoughts arise, gently return to your breath</li>
                                <li>Be kind to yourself throughout the practice</li>
                            </ul>
                        </div>

                        <div class="timer-display" id="timer-display">2:00</div>

                        <div class="timer-controls">
                            <button id="start-meditation" class="timer-btn start">Start Meditation</button>
                            <button id="stop-meditation" class="timer-btn stop hidden">Stop</button>
                        </div>

                        <div id="meditation-complete" class="task-complete-indicator hidden">
                            ✅ Meditation completed! +20 XP
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script>
        let breathingExercise = null;
        let meditationTimer = null;
        let selectedMood = null;

        document.addEventListener('DOMContentLoaded', function() {
            initializeTasks();
            setupTaskNavigation();
            setupBreathingExercise();
            setupJournal();
            setupMoodTracker();
            setupMeditation();
            
            // Check URL hash for direct navigation
            const hash = window.location.hash.substring(1);
            if (hash) {
                showTask(hash);
            }
        });

        function initializeTasks() {
            const userData = getUserData();
            if (!userData) {
                window.location.href = 'index.html';
                return;
            }

            // Check task completion status
            updateTaskCompletionStatus(userData);
            
            // Set random journal prompt
            document.getElementById('journal-prompt').textContent = getRandomJournalPrompt();
        }

        function updateTaskCompletionStatus(userData) {
            const today = getCurrentDate();
            
            // Check each task completion
            Object.keys(userData.dailyTasks).forEach(taskType => {
                const task = userData.dailyTasks[taskType];
                if (task.completed && task.date === today) {
                    markTaskAsCompleted(taskType);
                }
            });
        }

        function markTaskAsCompleted(taskType) {
            const completeIndicator = document.getElementById(`${taskType}-complete`);
            if (completeIndicator) {
                completeIndicator.classList.remove('hidden');
            }

            // Disable task controls
            const section = document.getElementById(`${taskType}-section`);
            const buttons = section.querySelectorAll('button');
            buttons.forEach(btn => {
                if (!btn.classList.contains('task-nav-btn')) {
                    btn.disabled = true;
                    btn.textContent = btn.textContent.includes('Completed') ? btn.textContent : 'Completed';
                }
            });
        }

        function setupTaskNavigation() {
            document.querySelectorAll('.task-nav-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const taskType = this.dataset.task;
                    showTask(taskType);
                });
            });
        }

        function showTask(taskType) {
            // Update navigation
            document.querySelectorAll('.task-nav-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-task="${taskType}"]`).classList.add('active');

            // Show/hide sections
            document.querySelectorAll('.task-section').forEach(section => {
                section.classList.add('hidden');
            });
            document.getElementById(`${taskType}-section`).classList.remove('hidden');
        }

        function setupBreathingExercise() {
            const startBtn = document.getElementById('start-breathing');
            const stopBtn = document.getElementById('stop-breathing');
            const container = document.getElementById('breathing-section');

            startBtn.addEventListener('click', function() {
                if (!breathingExercise) {
                    breathingExercise = new BreathingExercise(container);
                }
                breathingExercise.start();
                startBtn.classList.add('hidden');
                stopBtn.classList.remove('hidden');
            });

            stopBtn.addEventListener('click', function() {
                if (breathingExercise) {
                    breathingExercise.stop();
                }
                startBtn.classList.remove('hidden');
                stopBtn.classList.add('hidden');
            });
        }

        function setupJournal() {
            const textarea = document.getElementById('journal-entry');
            const saveBtn = document.getElementById('save-journal');
            const newPromptBtn = document.getElementById('new-prompt');

            textarea.addEventListener('input', function() {
                saveBtn.disabled = this.value.trim().length < 10;
            });

            saveBtn.addEventListener('click', function() {
                const entry = textarea.value.trim();
                if (entry.length >= 10) {
                    completeTaskWithRewards('journal', { entry });
                    markTaskAsCompleted('journal');
                    textarea.disabled = true;
                }
            });

            newPromptBtn.addEventListener('click', function() {
                document.getElementById('journal-prompt').textContent = getRandomJournalPrompt();
            });
        }

        function setupMoodTracker() {
            const moodItems = document.querySelectorAll('.mood-item');
            const saveBtn = document.getElementById('save-mood');
            const messageDiv = document.getElementById('mood-message');
            const responseP = document.getElementById('mood-response');

            moodItems.forEach(item => {
                item.addEventListener('click', function() {
                    moodItems.forEach(m => m.classList.remove('selected'));
                    this.classList.add('selected');
                    selectedMood = parseInt(this.dataset.mood);
                    saveBtn.disabled = false;

                    // Show mood response
                    const responses = [
                        "I understand you're feeling sad. Remember that difficult feelings are temporary, and you're taking a positive step by acknowledging them.",
                        "It's okay to have tough days. You're being brave by checking in with yourself.",
                        "You're doing okay, and that's perfectly fine. Every day doesn't have to be amazing.",
                        "I'm glad you're feeling happy! What brought joy to your day?",
                        "Wonderful! You're radiating positive energy. Keep embracing these beautiful moments!"
                    ];
                    
                    responseP.textContent = responses[selectedMood];
                    messageDiv.classList.remove('hidden');
                });
            });

            saveBtn.addEventListener('click', function() {
                if (selectedMood !== null) {
                    completeTaskWithRewards('mood', { value: selectedMood });
                    markTaskAsCompleted('mood');
                    moodItems.forEach(item => item.style.pointerEvents = 'none');
                }
            });
        }

        function setupMeditation() {
            const startBtn = document.getElementById('start-meditation');
            const stopBtn = document.getElementById('stop-meditation');
            const container = document.getElementById('meditation-section');

            startBtn.addEventListener('click', function() {
                if (!meditationTimer) {
                    meditationTimer = new MeditationTimer(container, 120); // 2 minutes
                }
                meditationTimer.start();
                startBtn.classList.add('hidden');
                stopBtn.classList.remove('hidden');
            });

            stopBtn.addEventListener('click', function() {
                if (meditationTimer) {
                    meditationTimer.stop();
                }
                startBtn.classList.remove('hidden');
                stopBtn.classList.add('hidden');
            });
        }
    </script>

    <style>
        .page-header {
            text-align: center;
            margin-bottom: 2rem;
        }

        .page-header h1 {
            color: white;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .page-header p {
            color: rgba(255,255,255,0.9);
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }

        .journal-prompt {
            background: rgba(102, 126, 234, 0.1);
            padding: 1rem;
            border-radius: 10px;
            font-style: italic;
            color: #667eea;
            font-weight: 600;
        }

        .mood-message {
            background: rgba(102, 126, 234, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border-left: 4px solid #667eea;
        }

        .meditation-instructions ul {
            text-align: left;
            max-width: 400px;
            margin: 0 auto 2rem auto;
        }

        .meditation-instructions li {
            margin-bottom: 0.5rem;
            color: #555;
        }

        @media (max-width: 768px) {
            .task-navigation {
                grid-template-columns: repeat(2, 1fr);
                display: grid;
            }
            
            .mood-grid {
                grid-template-columns: repeat(3, 1fr);
                display: grid;
            }
            
            .timer-display {
                font-size: 3rem;
            }
        }
    </style>
    <script src="js/enhanced-features.js"></script>
</body>
</html>
