<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analytics - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="analytics-page">
    <div class="container">
        <header class="dashboard-header">
            <div class="user-info">
                <div id="user-avatar" class="avatar-circle"></div>
                <div class="user-details">
                    <h2>Analytics Dashboard</h2>
                    <p class="page-subtitle">Track your wellness journey</p>
                </div>
            </div>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link active">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="analytics-content">
            <!-- Overview Stats -->
            <section class="analytics-overview">
                <div class="dashboard-card">
                    <h3>Wellness Overview</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📈</div>
                            <div class="stat-info">
                                <span class="stat-value" id="total-sessions">0</span>
                                <span class="stat-label">Total Sessions</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⏱️</div>
                            <div class="stat-info">
                                <span class="stat-value" id="total-time">0h</span>
                                <span class="stat-label">Time Invested</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🔥</div>
                            <div class="stat-info">
                                <span class="stat-value" id="current-streak">0</span>
                                <span class="stat-label">Current Streak</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-info">
                                <span class="stat-value" id="goals-completed">0</span>
                                <span class="stat-label">Goals Achieved</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Activity Chart -->
            <section class="activity-chart">
                <div class="dashboard-card">
                    <h3>Weekly Activity</h3>
                    <div class="chart-container">
                        <canvas id="activity-chart" width="400" height="200"></canvas>
                    </div>
                </div>
            </section>

            <!-- Mood Tracking -->
            <section class="mood-analytics">
                <div class="dashboard-card">
                    <h3>Mood Trends</h3>
                    <div class="mood-chart">
                        <div class="mood-grid" id="mood-grid">
                            <!-- Mood data will be populated here -->
                        </div>
                    </div>
                </div>
            </section>

            <!-- Task Completion -->
            <section class="task-analytics">
                <div class="dashboard-card">
                    <h3>Task Completion Rate</h3>
                    <div class="completion-stats">
                        <div class="completion-item">
                            <span class="task-type">Breathing Exercises</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="breathing-progress" style="width: 0%"></div>
                            </div>
                            <span class="completion-rate" id="breathing-rate">0%</span>
                        </div>
                        <div class="completion-item">
                            <span class="task-type">Journaling</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="journal-progress" style="width: 0%"></div>
                            </div>
                            <span class="completion-rate" id="journal-rate">0%</span>
                        </div>
                        <div class="completion-item">
                            <span class="task-type">Meditation</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="meditation-progress" style="width: 0%"></div>
                            </div>
                            <span class="completion-rate" id="meditation-rate">0%</span>
                        </div>
                        <div class="completion-item">
                            <span class="task-type">Exercise</span>
                            <div class="progress-bar">
                                <div class="progress-fill" id="exercise-progress" style="width: 0%"></div>
                            </div>
                            <span class="completion-rate" id="exercise-rate">0%</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Insights -->
            <section class="analytics-insights">
                <div class="dashboard-card">
                    <h3>Personal Insights</h3>
                    <div class="insights-list" id="insights-list">
                        <div class="insight-item">
                            <div class="insight-icon">💡</div>
                            <div class="insight-content">
                                <h4>Most Active Day</h4>
                                <p id="most-active-day">Complete more tasks to see insights</p>
                            </div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-icon">⭐</div>
                            <div class="insight-content">
                                <h4>Favorite Activity</h4>
                                <p id="favorite-activity">Complete more tasks to see insights</p>
                            </div>
                        </div>
                        <div class="insight-item">
                            <div class="insight-icon">📊</div>
                            <div class="insight-content">
                                <h4>Progress Trend</h4>
                                <p id="progress-trend">Complete more tasks to see insights</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/analytics.js"></script>
    <script>
        // Initialize analytics page
        document.addEventListener('DOMContentLoaded', function() {
            // Check authentication
            const currentUser = Auth.getCurrentUser();
            if (!currentUser) {
                window.location.href = 'signin.html';
                return;
            }

            initializeAnalytics();
        });

        function initializeAnalytics() {
            loadUserData();
            updateOverviewStats();
            generateActivityChart();
            updateMoodTrends();
            updateTaskCompletion();
            generateInsights();
        }

        function loadUserData() {
            const userData = getUserData();
            const currentUser = Auth.getCurrentUser();
            
            if (currentUser && userData) {
                document.getElementById('user-avatar').textContent = currentUser.avatar || '🧘';
                document.getElementById('user-avatar').className = `avatar-circle ${userData.avatar}-avatar`;
            }
        }

        function updateOverviewStats() {
            const userData = getUserData();
            if (!userData) return;

            // Calculate total sessions
            const totalSessions = (userData.completedTasks || []).length;
            document.getElementById('total-sessions').textContent = totalSessions;

            // Calculate total time (estimate based on tasks)
            const totalMinutes = totalSessions * 10; // Assume 10 minutes per task
            const hours = Math.floor(totalMinutes / 60);
            const minutes = totalMinutes % 60;
            document.getElementById('total-time').textContent = `${hours}h ${minutes}m`;

            // Current streak
            document.getElementById('current-streak').textContent = userData.streak || 0;

            // Goals completed
            const goalsCompleted = (userData.goals || []).filter(goal => goal.completed).length;
            document.getElementById('goals-completed').textContent = goalsCompleted;
        }

        function generateActivityChart() {
            // Simple activity chart using canvas
            const canvas = document.getElementById('activity-chart');
            const ctx = canvas.getContext('2d');
            
            // Sample data for the last 7 days
            const userData = getUserData();
            const activityData = generateWeeklyActivity(userData);
            
            drawActivityChart(ctx, activityData);
        }

        function generateWeeklyActivity(userData) {
            const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            const data = [];
            
            for (let i = 0; i < 7; i++) {
                // Generate sample activity data
                data.push(Math.floor(Math.random() * 5) + 1);
            }
            
            return { days, data };
        }

        function drawActivityChart(ctx, activityData) {
            const canvas = ctx.canvas;
            const width = canvas.width;
            const height = canvas.height;
            
            // Clear canvas
            ctx.clearRect(0, 0, width, height);
            
            // Set styles
            ctx.fillStyle = '#4CAF50';
            ctx.strokeStyle = '#2E7D32';
            ctx.lineWidth = 2;
            
            const barWidth = width / activityData.days.length;
            const maxValue = Math.max(...activityData.data);
            
            // Draw bars
            activityData.data.forEach((value, index) => {
                const barHeight = (value / maxValue) * (height - 40);
                const x = index * barWidth + 10;
                const y = height - barHeight - 20;
                
                ctx.fillRect(x, y, barWidth - 20, barHeight);
                
                // Draw day labels
                ctx.fillStyle = '#333';
                ctx.font = '12px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(activityData.days[index], x + (barWidth - 20) / 2, height - 5);
                
                // Reset fill style
                ctx.fillStyle = '#4CAF50';
            });
        }

        function updateMoodTrends() {
            const moodGrid = document.getElementById('mood-grid');
            const userData = getUserData();
            
            // Generate mood data for the last 30 days
            const moodData = generateMoodData(userData);
            
            moodGrid.innerHTML = '';
            moodData.forEach(mood => {
                const moodCell = document.createElement('div');
                moodCell.className = `mood-cell mood-${mood.level}`;
                moodCell.title = `${mood.date}: ${mood.emoji}`;
                moodCell.textContent = mood.emoji;
                moodGrid.appendChild(moodCell);
            });
        }

        function generateMoodData(userData) {
            const moods = ['😢', '😕', '😐', '😊', '😄'];
            const data = [];
            
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const level = Math.floor(Math.random() * 5);
                
                data.push({
                    date: date.toDateString(),
                    level: level,
                    emoji: moods[level]
                });
            }
            
            return data;
        }

        function updateTaskCompletion() {
            const userData = getUserData();
            if (!userData || !userData.completedTasks) return;
            
            const taskTypes = {
                breathing: 0,
                journal: 0,
                meditation: 0,
                exercise: 0
            };
            
            // Count completed tasks by type
            userData.completedTasks.forEach(task => {
                if (task.type && taskTypes.hasOwnProperty(task.type)) {
                    taskTypes[task.type]++;
                }
            });
            
            // Calculate completion rates (out of 30 days)
            Object.keys(taskTypes).forEach(type => {
                const rate = Math.min((taskTypes[type] / 30) * 100, 100);
                document.getElementById(`${type}-progress`).style.width = `${rate}%`;
                document.getElementById(`${type}-rate`).textContent = `${Math.round(rate)}%`;
            });
        }

        function generateInsights() {
            const userData = getUserData();
            if (!userData) return;
            
            // Most active day
            const mostActiveDay = 'Monday'; // This would be calculated from actual data
            document.getElementById('most-active-day').textContent = `You're most productive on ${mostActiveDay}s`;
            
            // Favorite activity
            const favoriteActivity = 'Breathing Exercises'; // This would be calculated from actual data
            document.getElementById('favorite-activity').textContent = `You enjoy ${favoriteActivity} the most`;
            
            // Progress trend
            const trend = 'improving'; // This would be calculated from actual data
            document.getElementById('progress-trend').textContent = `Your wellness journey is ${trend} steadily`;
        }
    </script>
</body>
</html>
