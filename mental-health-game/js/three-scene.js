// Three.js Scene Controller for VR Meditation Environments

class ThreeScene {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.controls = null;
        this.currentEnvironment = 'forest';
        this.isVRMode = false;
        this.animationId = null;
        this.environmentObjects = {};
        this.particles = null;
        this.ambientLight = null;
        this.directionalLight = null;
    }

    // Initialize Three.js scene
    async initialize() {
        try {
            this.setupScene();
            this.setupCamera();
            this.setupRenderer();
            this.setupLights();
            this.setupControls();
            
            // Load initial environment
            await this.loadEnvironment(this.currentEnvironment);
            
            // Start render loop
            this.startRenderLoop();
            
            console.log('Three.js scene initialized');
            return true;
        } catch (error) {
            console.error('Failed to initialize Three.js scene:', error);
            return false;
        }
    }

    // Setup Three.js scene
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 10, 1000);
    }

    // Setup camera
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, // FOV
            window.innerWidth / window.innerHeight, // Aspect ratio
            0.1, // Near plane
            1000 // Far plane
        );
        this.camera.position.set(0, 1.6, 5); // Human eye height
    }

    // Setup WebGL renderer with VR support
    setupRenderer() {
        const canvas = document.getElementById('vr-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            alpha: true 
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(window.devicePixelRatio);
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // Enable VR
        this.renderer.xr.enabled = true;
        
        // Add VR button
        const vrButton = VRButton.createButton(this.renderer);
        document.body.appendChild(vrButton);
    }

    // Setup lighting
    setupLights() {
        // Ambient light for overall illumination
        this.ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(this.ambientLight);

        // Directional light for shadows and depth
        this.directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        this.directionalLight.position.set(50, 50, 50);
        this.directionalLight.castShadow = true;
        this.directionalLight.shadow.mapSize.width = 2048;
        this.directionalLight.shadow.mapSize.height = 2048;
        this.directionalLight.shadow.camera.near = 0.5;
        this.directionalLight.shadow.camera.far = 500;
        this.scene.add(this.directionalLight);
    }

    // Setup camera controls for desktop mode
    setupControls() {
        // Only setup controls if not in VR mode
        if (!this.isVRMode && window.THREE.OrbitControls) {
            this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
            this.controls.enableDamping = true;
            this.controls.dampingFactor = 0.05;
            this.controls.maxPolarAngle = Math.PI / 2;
            this.controls.minDistance = 1;
            this.controls.maxDistance = 50;
        }
    }

    // Enable VR mode
    enableVR(session) {
        this.isVRMode = true;
        this.renderer.xr.setSession(session);
        
        // Disable orbit controls in VR
        if (this.controls) {
            this.controls.enabled = false;
        }
        
        console.log('VR mode enabled in Three.js');
    }

    // Load environment based on type
    async loadEnvironment(environmentType) {
        try {
            // Clear previous environment
            this.clearEnvironment();
            
            // Update current environment
            this.currentEnvironment = environmentType;
            
            // Load new environment
            switch (environmentType) {
                case 'forest':
                    await this.loadForestEnvironment();
                    break;
                case 'beach':
                    await this.loadBeachEnvironment();
                    break;
                case 'mountain':
                    await this.loadMountainEnvironment();
                    break;
                case 'space':
                    await this.loadSpaceEnvironment();
                    break;
                default:
                    await this.loadForestEnvironment();
            }
            
            // Add particles for ambiance
            this.addParticleSystem();
            
            console.log(`Loaded ${environmentType} environment`);
        } catch (error) {
            console.error(`Failed to load ${environmentType} environment:`, error);
        }
    }

    // Clear current environment
    clearEnvironment() {
        Object.values(this.environmentObjects).forEach(obj => {
            this.scene.remove(obj);
            if (obj.geometry) obj.geometry.dispose();
            if (obj.material) {
                if (Array.isArray(obj.material)) {
                    obj.material.forEach(mat => mat.dispose());
                } else {
                    obj.material.dispose();
                }
            }
        });
        this.environmentObjects = {};
        
        if (this.particles) {
            this.scene.remove(this.particles);
            this.particles = null;
        }
    }

    // Load forest environment
    async loadForestEnvironment() {
        // Create ground
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x3a5f3a });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        this.environmentObjects.ground = ground;

        // Create trees
        for (let i = 0; i < 50; i++) {
            const tree = this.createTree();
            tree.position.set(
                (Math.random() - 0.5) * 80,
                0,
                (Math.random() - 0.5) * 80
            );
            this.scene.add(tree);
            this.environmentObjects[`tree_${i}`] = tree;
        }

        // Update lighting for forest
        this.ambientLight.color.setHex(0x2d4a2d);
        this.directionalLight.color.setHex(0x9acd32);
        this.scene.fog.color.setHex(0x2d4a2d);
    }

    // Create a simple tree
    createTree() {
        const tree = new THREE.Group();
        
        // Trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.2, 0.3, 3);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 1.5;
        trunk.castShadow = true;
        tree.add(trunk);
        
        // Leaves
        const leavesGeometry = new THREE.SphereGeometry(2);
        const leavesMaterial = new THREE.MeshLambertMaterial({ color: 0x228b22 });
        const leaves = new THREE.Mesh(leavesGeometry, leavesMaterial);
        leaves.position.y = 4;
        leaves.castShadow = true;
        tree.add(leaves);
        
        return tree;
    }

    // Load beach environment
    async loadBeachEnvironment() {
        // Create sand
        const sandGeometry = new THREE.PlaneGeometry(100, 100);
        const sandMaterial = new THREE.MeshLambertMaterial({ color: 0xf4e4bc });
        const sand = new THREE.Mesh(sandGeometry, sandMaterial);
        sand.rotation.x = -Math.PI / 2;
        sand.receiveShadow = true;
        this.scene.add(sand);
        this.environmentObjects.sand = sand;

        // Create water
        const waterGeometry = new THREE.PlaneGeometry(100, 50);
        const waterMaterial = new THREE.MeshLambertMaterial({ 
            color: 0x006994,
            transparent: true,
            opacity: 0.8
        });
        const water = new THREE.Mesh(waterGeometry, waterMaterial);
        water.rotation.x = -Math.PI / 2;
        water.position.z = -25;
        this.scene.add(water);
        this.environmentObjects.water = water;

        // Add palm trees
        for (let i = 0; i < 10; i++) {
            const palm = this.createPalmTree();
            palm.position.set(
                (Math.random() - 0.5) * 40,
                0,
                Math.random() * 20 + 10
            );
            this.scene.add(palm);
            this.environmentObjects[`palm_${i}`] = palm;
        }

        // Update lighting for beach
        this.ambientLight.color.setHex(0x87ceeb);
        this.directionalLight.color.setHex(0xffd700);
        this.scene.fog.color.setHex(0x87ceeb);
    }

    // Create palm tree
    createPalmTree() {
        const palm = new THREE.Group();
        
        // Trunk
        const trunkGeometry = new THREE.CylinderGeometry(0.15, 0.2, 4);
        const trunkMaterial = new THREE.MeshLambertMaterial({ color: 0x8b4513 });
        const trunk = new THREE.Mesh(trunkGeometry, trunkMaterial);
        trunk.position.y = 2;
        trunk.castShadow = true;
        palm.add(trunk);
        
        // Palm fronds
        for (let i = 0; i < 6; i++) {
            const frondGeometry = new THREE.ConeGeometry(0.1, 3);
            const frondMaterial = new THREE.MeshLambertMaterial({ color: 0x228b22 });
            const frond = new THREE.Mesh(frondGeometry, frondMaterial);
            frond.position.y = 4;
            frond.rotation.z = (i / 6) * Math.PI * 2;
            frond.rotation.x = Math.PI / 6;
            palm.add(frond);
        }
        
        return palm;
    }

    // Load mountain environment
    async loadMountainEnvironment() {
        // Create rocky ground
        const groundGeometry = new THREE.PlaneGeometry(100, 100);
        const groundMaterial = new THREE.MeshLambertMaterial({ color: 0x696969 });
        const ground = new THREE.Mesh(groundGeometry, groundMaterial);
        ground.rotation.x = -Math.PI / 2;
        ground.receiveShadow = true;
        this.scene.add(ground);
        this.environmentObjects.ground = ground;

        // Create mountains
        for (let i = 0; i < 8; i++) {
            const mountain = this.createMountain();
            mountain.position.set(
                (Math.random() - 0.5) * 80,
                0,
                -30 - Math.random() * 20
            );
            this.scene.add(mountain);
            this.environmentObjects[`mountain_${i}`] = mountain;
        }

        // Update lighting for mountains
        this.ambientLight.color.setHex(0x4682b4);
        this.directionalLight.color.setHex(0xffffff);
        this.scene.fog.color.setHex(0x4682b4);
    }

    // Create mountain
    createMountain() {
        const mountainGeometry = new THREE.ConeGeometry(
            Math.random() * 10 + 5,
            Math.random() * 20 + 15,
            8
        );
        const mountainMaterial = new THREE.MeshLambertMaterial({ color: 0x708090 });
        const mountain = new THREE.Mesh(mountainGeometry, mountainMaterial);
        mountain.position.y = mountain.geometry.parameters.height / 2;
        mountain.castShadow = true;
        return mountain;
    }

    // Load space environment
    async loadSpaceEnvironment() {
        // Create starfield
        const starGeometry = new THREE.BufferGeometry();
        const starMaterial = new THREE.PointsMaterial({ color: 0xffffff, size: 2 });
        
        const starVertices = [];
        for (let i = 0; i < 10000; i++) {
            starVertices.push(
                (Math.random() - 0.5) * 2000,
                (Math.random() - 0.5) * 2000,
                (Math.random() - 0.5) * 2000
            );
        }
        
        starGeometry.setAttribute('position', new THREE.Float32BufferAttribute(starVertices, 3));
        const stars = new THREE.Points(starGeometry, starMaterial);
        this.scene.add(stars);
        this.environmentObjects.stars = stars;

        // Create floating platform
        const platformGeometry = new THREE.CylinderGeometry(5, 5, 0.5);
        const platformMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
        const platform = new THREE.Mesh(platformGeometry, platformMaterial);
        platform.position.y = -0.25;
        platform.receiveShadow = true;
        this.scene.add(platform);
        this.environmentObjects.platform = platform;

        // Update lighting for space
        this.ambientLight.color.setHex(0x1a1a2e);
        this.directionalLight.color.setHex(0x9d4edd);
        this.scene.fog.color.setHex(0x0f0f23);
    }

    // Add particle system for ambiance
    addParticleSystem() {
        const particleCount = 1000;
        const particles = new THREE.BufferGeometry();
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xffffff,
            size: 0.1,
            transparent: true,
            opacity: 0.6
        });

        const positions = [];
        for (let i = 0; i < particleCount; i++) {
            positions.push(
                (Math.random() - 0.5) * 100,
                Math.random() * 50,
                (Math.random() - 0.5) * 100
            );
        }

        particles.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
        this.particles = new THREE.Points(particles, particleMaterial);
        this.scene.add(this.particles);
    }

    // Change environment
    async changeEnvironment(environmentType) {
        if (environmentType !== this.currentEnvironment) {
            await this.loadEnvironment(environmentType);
        }
    }

    // Start render loop
    startRenderLoop() {
        const animate = () => {
            this.animationId = this.renderer.setAnimationLoop(animate);
            
            // Update controls if not in VR
            if (this.controls && !this.isVRMode) {
                this.controls.update();
            }
            
            // Animate particles
            if (this.particles) {
                this.particles.rotation.y += 0.001;
            }
            
            // Animate environment objects
            this.animateEnvironment();
            
            // Render scene
            this.renderer.render(this.scene, this.camera);
        };
        
        animate();
    }

    // Animate environment objects
    animateEnvironment() {
        const time = Date.now() * 0.001;
        
        // Animate based on current environment
        switch (this.currentEnvironment) {
            case 'beach':
                // Animate water
                if (this.environmentObjects.water) {
                    this.environmentObjects.water.material.opacity = 0.8 + Math.sin(time) * 0.1;
                }
                break;
            case 'space':
                // Rotate stars
                if (this.environmentObjects.stars) {
                    this.environmentObjects.stars.rotation.y += 0.0005;
                }
                break;
        }
    }

    // Handle window resize
    onWindowResize() {
        if (!this.isVRMode) {
            this.camera.aspect = window.innerWidth / window.innerHeight;
            this.camera.updateProjectionMatrix();
            this.renderer.setSize(window.innerWidth, window.innerHeight);
        }
    }

    // Dispose of Three.js resources
    dispose() {
        if (this.animationId) {
            this.renderer.setAnimationLoop(null);
        }
        
        this.clearEnvironment();
        
        if (this.renderer) {
            this.renderer.dispose();
        }
        
        console.log('Three.js scene disposed');
    }
}

// Create global Three.js scene instance
window.ThreeScene = new ThreeScene();

// Handle window resize
window.addEventListener('resize', () => {
    if (window.ThreeScene) {
        window.ThreeScene.onWindowResize();
    }
});
