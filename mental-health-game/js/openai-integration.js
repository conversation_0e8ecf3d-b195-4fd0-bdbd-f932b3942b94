// OpenAI Integration Module
// This file handles AI-powered features using OpenAI API

class OpenAIIntegration {
    constructor() {
        this.apiKey = null;
        this.baseURL = 'https://api.openai.com/v1';
        this.model = 'gpt-3.5-turbo';
        this.init();
    }

    init() {
        this.loadAPIKey();
        this.setupErrorHandling();
    }

    // Load API key from storage or prompt user
    loadAPIKey() {
        this.apiKey = localStorage.getItem('openai_api_key');
        
        // Check if API key is available from API_key.js
        if (typeof OPENAI_API_KEY !== 'undefined' && OPENAI_API_KEY) {
            this.apiKey = OPENAI_API_KEY;
        }
    }

    // Set API key
    setAPIKey(key) {
        this.apiKey = key;
        localStorage.setItem('openai_api_key', key);
    }

    // Check if API key is available
    hasAPIKey() {
        return this.apiKey && this.apiKey.trim() !== '';
    }

    // Prompt user for API key
    promptForAPIKey() {
        const key = prompt('Please enter your OpenAI API key to enable AI features:');
        if (key) {
            this.setAPIKey(key);
            return true;
        }
        return false;
    }

    // Make API request to OpenAI
    async makeRequest(messages, options = {}) {
        if (!this.hasAPIKey()) {
            throw new Error('OpenAI API key not configured');
        }

        const requestBody = {
            model: options.model || this.model,
            messages: messages,
            max_tokens: options.maxTokens || 150,
            temperature: options.temperature || 0.7,
            ...options
        };

        try {
            const response = await fetch(`${this.baseURL}/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.apiKey}`
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const error = await response.json();
                throw new Error(error.error?.message || 'API request failed');
            }

            const data = await response.json();
            return data.choices[0]?.message?.content || '';
        } catch (error) {
            console.error('OpenAI API Error:', error);
            throw error;
        }
    }

    // Generate wellness advice
    async generateWellnessAdvice(userContext = {}) {
        const { mood, goals, recentActivities, challenges } = userContext;
        
        const systemPrompt = `You are a compassionate wellness coach and mental health companion. 
        Provide personalized, supportive advice based on the user's current state. 
        Keep responses encouraging, practical, and under 150 words.`;

        const userPrompt = `Current mood: ${mood || 'neutral'}
        Goals: ${goals || 'general wellness'}
        Recent activities: ${recentActivities || 'none specified'}
        Current challenges: ${challenges || 'none specified'}
        
        Please provide personalized wellness advice.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Generate meditation guidance
    async generateMeditationGuidance(duration = 5, type = 'mindfulness') {
        const systemPrompt = `You are a meditation instructor. Create a guided meditation script 
        that is calming, clear, and appropriate for the specified duration and type.`;

        const userPrompt = `Create a ${duration}-minute ${type} meditation guidance. 
        Include breathing instructions and mindfulness cues.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages, { maxTokens: 300 });
    }

    // Analyze journal entry
    async analyzeJournalEntry(entry) {
        const systemPrompt = `You are a supportive wellness companion. Analyze the journal entry 
        and provide gentle insights, emotional validation, and constructive suggestions. 
        Be empathetic and encouraging.`;

        const userPrompt = `Please analyze this journal entry and provide supportive insights: "${entry}"`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages, { maxTokens: 200 });
    }

    // Generate personalized affirmations
    async generateAffirmations(userProfile = {}) {
        const { goals, challenges, strengths } = userProfile;
        
        const systemPrompt = `Create 3 personalized positive affirmations based on the user's profile. 
        Make them empowering, specific, and in first person ("I am...", "I can...", etc.).`;

        const userPrompt = `User goals: ${goals || 'personal growth'}
        Challenges: ${challenges || 'general life stress'}
        Strengths: ${strengths || 'resilience'}
        
        Generate 3 personalized affirmations.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Chat with AI companion
    async chatWithCompanion(userMessage, conversationHistory = []) {
        const systemPrompt = `You are a supportive AI wellness companion named Zen. 
        You're empathetic, encouraging, and knowledgeable about mental health and wellness. 
        Provide helpful, caring responses while maintaining appropriate boundaries. 
        If someone expresses serious mental health concerns, gently suggest professional help.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            ...conversationHistory.slice(-10), // Keep last 10 messages for context
            { role: 'user', content: userMessage }
        ];

        return await this.makeRequest(messages, { maxTokens: 200 });
    }

    // Generate sleep insights
    async generateSleepInsights(sleepData) {
        const { averageHours, quality, bedtime, wakeTime } = sleepData;
        
        const systemPrompt = `You are a sleep wellness expert. Analyze sleep data and provide 
        helpful insights and recommendations for better sleep hygiene.`;

        const userPrompt = `Sleep data:
        Average hours: ${averageHours}
        Quality rating: ${quality}/10
        Typical bedtime: ${bedtime}
        Typical wake time: ${wakeTime}
        
        Provide sleep insights and recommendations.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Generate nutrition suggestions
    async generateNutritionSuggestions(nutritionData) {
        const { goals, restrictions, currentHabits } = nutritionData;
        
        const systemPrompt = `You are a nutrition wellness coach. Provide practical, 
        healthy nutrition suggestions based on the user's goals and restrictions.`;

        const userPrompt = `Nutrition profile:
        Goals: ${goals || 'general health'}
        Dietary restrictions: ${restrictions || 'none'}
        Current habits: ${currentHabits || 'mixed'}
        
        Suggest 3 practical nutrition improvements.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Generate mood insights
    async generateMoodInsights(moodData) {
        const { recentMoods, patterns, triggers } = moodData;
        
        const systemPrompt = `You are a wellness coach specializing in emotional intelligence. 
        Analyze mood patterns and provide supportive insights and coping strategies.`;

        const userPrompt = `Mood data:
        Recent moods: ${recentMoods || 'varied'}
        Patterns noticed: ${patterns || 'none identified'}
        Potential triggers: ${triggers || 'unknown'}
        
        Provide mood insights and coping strategies.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Generate goal suggestions
    async generateGoalSuggestions(userProfile) {
        const { currentGoals, interests, challenges } = userProfile;
        
        const systemPrompt = `You are a life coach specializing in wellness goals. 
        Suggest SMART (Specific, Measurable, Achievable, Relevant, Time-bound) wellness goals.`;

        const userPrompt = `User profile:
        Current goals: ${currentGoals || 'none set'}
        Interests: ${interests || 'general wellness'}
        Challenges: ${challenges || 'time management'}
        
        Suggest 3 SMART wellness goals.`;

        const messages = [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
        ];

        return await this.makeRequest(messages);
    }

    // Error handling
    setupErrorHandling() {
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason?.message?.includes('API key')) {
                console.warn('OpenAI API key issue detected');
                this.handleAPIKeyError();
            }
        });
    }

    handleAPIKeyError() {
        const message = `
            AI features require an OpenAI API key. 
            You can get one from https://platform.openai.com/api-keys
            
            Would you like to enter your API key now?
        `;
        
        if (confirm(message)) {
            this.promptForAPIKey();
        }
    }

    // Utility methods
    async testConnection() {
        try {
            const response = await this.makeRequest([
                { role: 'user', content: 'Hello, please respond with "Connection successful"' }
            ], { maxTokens: 10 });
            
            return response.includes('successful');
        } catch (error) {
            return false;
        }
    }

    // Get usage statistics (mock implementation)
    getUsageStats() {
        const stats = JSON.parse(localStorage.getItem('openai_usage_stats') || '{}');
        return {
            totalRequests: stats.totalRequests || 0,
            totalTokens: stats.totalTokens || 0,
            lastUsed: stats.lastUsed || null
        };
    }

    // Update usage statistics
    updateUsageStats(tokens = 0) {
        const stats = this.getUsageStats();
        stats.totalRequests += 1;
        stats.totalTokens += tokens;
        stats.lastUsed = new Date().toISOString();
        
        localStorage.setItem('openai_usage_stats', JSON.stringify(stats));
    }

    // Clear API key and data
    clearAPIKey() {
        this.apiKey = null;
        localStorage.removeItem('openai_api_key');
        localStorage.removeItem('openai_usage_stats');
    }
}

// Create global instance
window.OpenAI = new OpenAIIntegration();

// Helper function for easy access
window.generateAIResponse = async function(prompt, options = {}) {
    try {
        if (!window.OpenAI.hasAPIKey()) {
            if (!window.OpenAI.promptForAPIKey()) {
                return 'AI features require an API key to function.';
            }
        }
        
        const messages = [{ role: 'user', content: prompt }];
        return await window.OpenAI.makeRequest(messages, options);
    } catch (error) {
        console.error('AI Response Error:', error);
        return 'Sorry, I encountered an error. Please try again later.';
    }
};

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = OpenAIIntegration;
}
