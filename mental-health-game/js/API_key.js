// API Key Configuration for MindJourney Enhanced
// 🔐 SECURITY NOTICE: Keep this file private and never commit to version control

/**
 * OpenAI API Configuration
 * Replace 'your-api-key-here' with your actual OpenAI API key
 * 
 * To get your API key:
 * 1. Visit https://platform.openai.com/api-keys
 * 2. Sign in or create an account
 * 3. Click "Create new secret key"
 * 4. Copy the key (starts with sk-proj- or sk-)
 * 5. Replace the placeholder below
 */

let API_CONFIG = {
    // 🔑 Replace this with your actual OpenAI API key


    OPENAI_API_KEY: '********************************************************************************************************************************************************************';
    
    // 🤖 AI Model Configuration
    MODEL_SETTINGS: {
        // Primary model for conversations and analysis
        primary: 'gpt-3.5-turbo',
        
        // Alternative model for complex analysis (if you have access)
        advanced: 'gpt-4',
        
        // Model for quick responses
        quick: 'gpt-3.5-turbo',
        
        // Maximum tokens per request
        maxTokens: 500,
        
        // Temperature for creativity (0.0 = focused, 1.0 = creative)
        temperature: 0.7,
        
        // Presence penalty to encourage diverse responses
        presencePenalty: 0.1,
        
        // Frequency penalty to reduce repetition
        frequencyPenalty: 0.1
    },
    
    // 🎯 Feature Flags - Enable/disable specific AI features
    FEATURES: {
        // Core AI features
        enhancedConversations: true,
        emotionalAnalysis: true,
        personalizedPrompts: true,
        behavioralInsights: true,
        predictiveAnalytics: true,
        
        // Advanced features
        voiceInteraction: false, // Future feature
        biometricIntegration: false, // Future feature
        socialSharing: false, // Future feature
        professionalReports: true,
        
        // Experimental features
        dreamAnalysis: true,
        nutritionTracking: true,
        exerciseRecommendations: true,
        sleepOptimization: true,
        stressDetection: true
    },
    
    // 🔒 Privacy Settings
    PRIVACY: {
        // What data to include in AI requests
        includePersonalName: true,
        includeMoodHistory: true,
        includeJournalContent: false, // Set to false for extra privacy
        includeGoalDetails: true,
        includeActivityStats: true,
        
        // Data retention
        conversationHistoryDays: 7, // How long to keep conversation history
        analyticsRetentionDays: 30, // How long to keep analytics data
        
        // Anonymization
        anonymizeData: false, // Remove personal identifiers before sending to AI
        useHashedIdentifiers: false // Use hashed IDs instead of real names
    },
    
    // ⚡ Performance Settings
    PERFORMANCE: {
        // Request timeouts
        requestTimeout: 30000, // 30 seconds
        retryAttempts: 3,
        retryDelay: 1000, // 1 second
        
        // Caching
        enableCaching: true,
        cacheExpiryMinutes: 60,
        
        // Rate limiting
        maxRequestsPerMinute: 20,
        maxRequestsPerHour: 100
    },
    
    // 🎨 Customization Options
    CUSTOMIZATION: {
        // AI personality settings
        aiPersonality: 'supportive', // supportive, professional, friendly, wise
        responseLength: 'medium', // short, medium, long
        technicalLevel: 'beginner', // beginner, intermediate, advanced
        
        // Language and tone
        language: 'en', // Language code
        culturalContext: 'general', // general, specific region
        ageGroup: 'adult', // teen, adult, senior
        
        // Specialized modes
        therapyMode: false, // More clinical approach
        coachingMode: true, // Motivational approach
        mindfulnessMode: true, // Meditation-focused
        crisisMode: true // Enhanced crisis detection
    }
};

// 🔐 Security validation
function validateAPIKey() {
    const key = API_CONFIG.OPENAI_API_KEY;
    
    if (!key || key === 'your-api-key-here') {
        console.warn('⚠️ OpenAI API key not configured. Please update API_key.js with your actual API key.');
        return false;
    }
    
    if (!key.startsWith('sk-')) {
        console.error('❌ Invalid API key format. OpenAI keys should start with "sk-"');
        return false;
    }
    
    if (key.length < 20) {
        console.error('❌ API key appears to be too short. Please check your key.');
        return false;
    }
    
    return true;
}

// 🚀 Initialize API configuration
function initializeAPIConfig() {
    if (validateAPIKey()) {
        console.log('✅ API configuration loaded successfully');
        
        // Set up OpenAI integration with the provided key
        if (window.openAI) {
            window.openAI.setApiKey(API_CONFIG.OPENAI_API_KEY);
            
            // Apply model settings
            window.openAI.model = API_CONFIG.MODEL_SETTINGS.primary;
            window.openAI.maxTokens = API_CONFIG.MODEL_SETTINGS.maxTokens;
            window.openAI.temperature = API_CONFIG.MODEL_SETTINGS.temperature;
            
            console.log('🤖 OpenAI integration configured');
        }
        
        return true;
    } else {
        console.log('⚠️ Running in basic mode without AI features');
        return false;
    }
}

// 🎯 Feature availability checker
function isFeatureEnabled(featureName) {
    return API_CONFIG.FEATURES[featureName] === true && validateAPIKey();
}

// 🔒 Privacy-aware data preparation
function prepareDataForAI(userData, includePersonalData = true) {
    const config = API_CONFIG.PRIVACY;
    
    const safeData = {
        level: userData.level,
        xp: userData.xp,
        streak: userData.streak,
        totalDays: userData.totalDays,
        stats: userData.stats
    };
    
    if (includePersonalData) {
        if (config.includePersonalName) {
            safeData.username = config.anonymizeData ? 'User' : userData.username;
        }
        
        if (config.includeMoodHistory) {
            safeData.moodHistory = userData.moodHistory?.slice(-7) || []; // Last 7 days only
        }
        
        if (config.includeJournalContent) {
            safeData.journalEntries = userData.journalEntries?.slice(-3) || []; // Last 3 entries only
        }
        
        if (config.includeGoalDetails) {
            safeData.goals = userData.goals?.filter(g => !g.completed).slice(0, 3) || []; // Active goals only
        }
        
        if (config.includeActivityStats) {
            safeData.recentActivity = userData.dailyTasks || {};
        }
    }
    
    return safeData;
}

// 📊 Usage tracking (for cost monitoring)
let usageTracker = {
    requestsToday: 0,
    requestsThisHour: 0,
    lastRequestTime: null,
    totalTokensUsed: 0,
    estimatedCost: 0
};

function trackAPIUsage(tokens = 0) {
    const now = new Date();
    const today = now.toDateString();
    const hour = now.getHours();
    
    // Reset daily counter
    if (usageTracker.lastRequestTime && 
        new Date(usageTracker.lastRequestTime).toDateString() !== today) {
        usageTracker.requestsToday = 0;
    }
    
    // Reset hourly counter
    if (usageTracker.lastRequestTime && 
        new Date(usageTracker.lastRequestTime).getHours() !== hour) {
        usageTracker.requestsThisHour = 0;
    }
    
    usageTracker.requestsToday++;
    usageTracker.requestsThisHour++;
    usageTracker.lastRequestTime = now;
    usageTracker.totalTokensUsed += tokens;
    
    // Estimate cost (approximate GPT-3.5-turbo pricing)
    usageTracker.estimatedCost = (usageTracker.totalTokensUsed / 1000) * 0.002;
    
    // Save to localStorage for persistence
    localStorage.setItem('mindjourney_usage', JSON.stringify(usageTracker));
}

function getUsageStats() {
    const saved = localStorage.getItem('mindjourney_usage');
    if (saved) {
        usageTracker = { ...usageTracker, ...JSON.parse(saved) };
    }
    return usageTracker;
}

function checkRateLimit() {
    const config = API_CONFIG.PERFORMANCE;
    
    if (usageTracker.requestsPerMinute >= config.maxRequestsPerMinute) {
        console.warn('⚠️ Rate limit reached for this minute');
        return false;
    }
    
    if (usageTracker.requestsThisHour >= config.maxRequestsPerHour) {
        console.warn('⚠️ Hourly rate limit reached');
        return false;
    }
    
    return true;
}

// 🎨 Get AI personality prompt based on configuration
function getPersonalityPrompt() {
    const personality = API_CONFIG.CUSTOMIZATION.aiPersonality;
    const responseLength = API_CONFIG.CUSTOMIZATION.responseLength;
    const technicalLevel = API_CONFIG.CUSTOMIZATION.technicalLevel;
    
    const personalities = {
        supportive: "You are a warm, empathetic AI companion focused on emotional support and encouragement.",
        professional: "You are a professional wellness coach with clinical knowledge and evidence-based approaches.",
        friendly: "You are a friendly, approachable AI buddy who makes wellness fun and engaging.",
        wise: "You are a wise, thoughtful AI mentor with deep insights into human psychology and wellness."
    };
    
    const lengths = {
        short: "Keep responses concise (1-2 sentences).",
        medium: "Provide moderate detail (2-4 sentences).",
        long: "Give comprehensive responses (4-6 sentences)."
    };
    
    const levels = {
        beginner: "Use simple language and explain concepts clearly.",
        intermediate: "Use moderate technical terms with brief explanations.",
        advanced: "Use professional terminology and assume familiarity with wellness concepts."
    };
    
    return `${personalities[personality]} ${lengths[responseLength]} ${levels[technicalLevel]}`;
}

// 🚀 Auto-initialize when script loads
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure other scripts are loaded
    setTimeout(() => {
        initializeAPIConfig();
        getUsageStats(); // Load saved usage data
    }, 100);
});

// 📤 Export configuration for use in other modules
window.API_CONFIG = API_CONFIG;
window.validateAPIKey = validateAPIKey;
window.initializeAPIConfig = initializeAPIConfig;
window.isFeatureEnabled = isFeatureEnabled;
window.prepareDataForAI = prepareDataForAI;
window.trackAPIUsage = trackAPIUsage;
window.getUsageStats = getUsageStats;
window.checkRateLimit = checkRateLimit;
window.getPersonalityPrompt = getPersonalityPrompt;

// 💡 Helper function to check if AI features are available
window.isAIAvailable = function() {
    return validateAPIKey() && window.openAI && window.openAI.isConfigured;
};

console.log('🔑 API configuration module loaded');

/*
📝 SETUP INSTRUCTIONS:

1. Replace 'your-api-key-here' with your actual OpenAI API key
2. Customize the settings above to match your preferences
3. Save this file and refresh your application
4. The AI features will automatically activate

🔒 SECURITY REMINDERS:
- Never share this file or commit it to version control
- Keep your API key private and secure
- Monitor your usage and costs regularly
- Revoke and regenerate keys if compromised

💰 COST MONITORING:
- Check usage stats in the browser console
- Monitor costs at https://platform.openai.com/usage
- Set up billing alerts in your OpenAI account
- Adjust rate limits if needed

🎯 CUSTOMIZATION:
- Modify FEATURES to enable/disable specific capabilities
- Adjust PRIVACY settings for your comfort level
- Customize AI personality and response style
- Set performance limits based on your usage needs
*/
