// Goals Management System for MindJourney

class GoalsManager {
    constructor() {
        this.userData = null;
        this.selectedGoalType = null;
        this.goalTemplates = {
            '7-day-streak': {
                title: '7-Day Consistency Challenge',
                description: 'Complete all daily tasks for 7 consecutive days',
                type: 'streak',
                target: 7,
                deadline: this.getDateInDays(7)
            },
            'mood-improvement': {
                title: 'Mood Boost Goal',
                description: 'Achieve an average mood of "Happy" or better for one week',
                type: 'mood',
                target: 3,
                deadline: this.getDateInDays(7)
            },
            'meditation-master': {
                title: 'Meditation Master',
                description: 'Complete 60 minutes of meditation this month',
                type: 'activity',
                activity: 'meditation',
                target: 60,
                deadline: this.getDateInDays(30)
            },
            'journal-journey': {
                title: 'Journal Journey',
                description: 'Write 10 meaningful journal entries',
                type: 'activity',
                activity: 'journal',
                target: 10,
                deadline: this.getDateInDays(14)
            },
            'breathing-basics': {
                title: 'Breathing Basics',
                description: 'Complete 20 breathing exercises',
                type: 'activity',
                activity: 'breathing',
                target: 20,
                deadline: this.getDateInDays(21)
            },
            'wellness-warrior': {
                title: 'Wellness Warrior',
                description: 'Reach level 5 in your wellness journey',
                type: 'level',
                target: 5,
                deadline: this.getDateInDays(30)
            }
        };
    }

    initialize() {
        this.userData = getUserData();
        if (!this.userData) {
            window.location.href = 'index.html';
            return;
        }

        // Initialize goals array if it doesn't exist
        if (!this.userData.goals) {
            this.userData.goals = [];
            saveUserData(this.userData);
        }

        this.loadGoals();
        this.setupEventListeners();
    }

    loadGoals() {
        this.displayActiveGoals();
        this.displayCompletedGoals();
    }

    displayActiveGoals() {
        const activeGoalsContainer = document.getElementById('active-goals');
        const activeGoals = this.userData.goals.filter(goal => !goal.completed);

        if (activeGoals.length === 0) {
            activeGoalsContainer.innerHTML = '<div class="no-goals"><p>No active goals yet. Create your first wellness goal below!</p></div>';
            return;
        }

        activeGoalsContainer.innerHTML = activeGoals.map(goal => this.createGoalCard(goal)).join('');
    }

    displayCompletedGoals() {
        const completedGoalsContainer = document.getElementById('completed-goals');
        const completedGoals = this.userData.goals.filter(goal => goal.completed);

        if (completedGoals.length === 0) {
            completedGoalsContainer.innerHTML = '<div class="no-goals"><p>Complete your first goal to see it here!</p></div>';
            return;
        }

        completedGoalsContainer.innerHTML = completedGoals.map(goal => this.createGoalCard(goal)).join('');
    }

    createGoalCard(goal) {
        const progress = this.calculateGoalProgress(goal);
        const isOverdue = new Date(goal.deadline) < new Date() && !goal.completed;
        
        return `
            <div class="goal-card ${goal.completed ? 'completed' : ''}" data-goal-id="${goal.id}">
                <div class="goal-header">
                    <h3 class="goal-title">${goal.title}</h3>
                    <span class="goal-status ${goal.completed ? 'completed' : 'active'}">
                        ${goal.completed ? 'Completed' : isOverdue ? 'Overdue' : 'Active'}
                    </span>
                </div>
                
                <p class="goal-description">${goal.description}</p>
                
                <div class="goal-progress">
                    <div class="progress-info">
                        <span class="progress-text">${this.getProgressText(goal, progress)}</span>
                        <span class="progress-percentage">${Math.round(progress)}%</span>
                    </div>
                    <div class="progress-container">
                        <div class="progress-bar" style="width: ${Math.min(progress, 100)}%"></div>
                    </div>
                </div>
                
                <div class="goal-meta">
                    <small>Deadline: ${this.formatDate(goal.deadline)}</small>
                    ${goal.completed ? `<small>Completed: ${this.formatDate(goal.completedDate)}</small>` : ''}
                </div>
                
                <div class="goal-actions">
                    ${!goal.completed && progress >= 100 ? 
                        `<button class="goal-action-btn complete-btn" onclick="goalsManager.completeGoal('${goal.id}')">Mark Complete</button>` : ''}
                    ${!goal.completed ? 
                        `<button class="goal-action-btn edit-btn" onclick="goalsManager.editGoal('${goal.id}')">Edit</button>` : ''}
                    <button class="goal-action-btn delete-btn" onclick="goalsManager.deleteGoal('${goal.id}')">Delete</button>
                </div>
            </div>
        `;
    }

    calculateGoalProgress(goal) {
        const userData = this.userData;
        
        switch (goal.type) {
            case 'streak':
                return Math.min((userData.streak / goal.target) * 100, 100);
                
            case 'activity':
                let current = 0;
                switch (goal.activity) {
                    case 'breathing':
                        current = userData.stats.totalBreathingSessions;
                        break;
                    case 'journal':
                        current = userData.stats.totalJournalEntries;
                        break;
                    case 'meditation':
                        current = userData.stats.totalMeditationMinutes;
                        break;
                    case 'mood':
                        current = userData.stats.totalMoodEntries;
                        break;
                }
                return Math.min((current / goal.target) * 100, 100);
                
            case 'mood':
                // Calculate average mood over the goal period
                const recentMoods = userData.moodHistory.slice(-7); // Last 7 days
                if (recentMoods.length === 0) return 0;
                const avgMood = recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length;
                return Math.min((avgMood / goal.target) * 100, 100);
                
            case 'level':
                return Math.min((userData.level / goal.target) * 100, 100);
                
            case 'custom':
                return goal.progress || 0;
                
            default:
                return 0;
        }
    }

    getProgressText(goal, progress) {
        switch (goal.type) {
            case 'streak':
                return `${this.userData.streak} / ${goal.target} days`;
            case 'activity':
                let current = 0;
                switch (goal.activity) {
                    case 'breathing':
                        current = this.userData.stats.totalBreathingSessions;
                        break;
                    case 'journal':
                        current = this.userData.stats.totalJournalEntries;
                        break;
                    case 'meditation':
                        current = this.userData.stats.totalMeditationMinutes;
                        break;
                    case 'mood':
                        current = this.userData.stats.totalMoodEntries;
                        break;
                }
                return `${current} / ${goal.target} ${goal.activity === 'meditation' ? 'minutes' : goal.activity === 'breathing' ? 'sessions' : 'entries'}`;
            case 'mood':
                const recentMoods = this.userData.moodHistory.slice(-7);
                const avgMood = recentMoods.length > 0 
                    ? (recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length).toFixed(1)
                    : '0.0';
                return `Average mood: ${avgMood} / ${goal.target}`;
            case 'level':
                return `Level ${this.userData.level} / ${goal.target}`;
            case 'custom':
                return goal.progressText || `${Math.round(progress)}% complete`;
            default:
                return 'Progress tracking unavailable';
        }
    }

    setupEventListeners() {
        // Goal type selection
        document.querySelectorAll('.goal-type-card').forEach(card => {
            card.addEventListener('click', () => {
                document.querySelectorAll('.goal-type-card').forEach(c => c.classList.remove('selected'));
                card.classList.add('selected');
                this.selectedGoalType = card.dataset.type;
                this.showGoalForm();
            });
        });

        // Form actions
        document.getElementById('create-goal').addEventListener('click', () => this.createGoal());
        document.getElementById('cancel-goal').addEventListener('click', () => this.hideGoalForm());
        document.getElementById('ai-suggest-goal').addEventListener('click', () => this.suggestGoalWithAI());

        // Template buttons
        document.querySelectorAll('.template-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const templateId = e.target.closest('.template-card').dataset.template;
                this.useTemplate(templateId);
            });
        });
    }

    showGoalForm() {
        const form = document.getElementById('goal-form');
        const parameters = document.getElementById('goal-parameters');
        
        form.classList.remove('hidden');
        
        // Clear previous parameters
        parameters.innerHTML = '';
        
        // Add type-specific parameters
        switch (this.selectedGoalType) {
            case 'streak':
                parameters.innerHTML = `
                    <div class="parameter-group">
                        <label for="streak-target">Target Streak (days):</label>
                        <input type="number" id="streak-target" min="1" max="365" value="7">
                    </div>
                `;
                break;
                
            case 'activity':
                parameters.innerHTML = `
                    <div class="parameter-group">
                        <label for="activity-type">Activity Type:</label>
                        <select id="activity-type">
                            <option value="breathing">Breathing Sessions</option>
                            <option value="journal">Journal Entries</option>
                            <option value="meditation">Meditation Minutes</option>
                            <option value="mood">Mood Check-ins</option>
                        </select>
                    </div>
                    <div class="parameter-group">
                        <label for="activity-target">Target Amount:</label>
                        <input type="number" id="activity-target" min="1" value="10">
                    </div>
                `;
                break;
                
            case 'mood':
                parameters.innerHTML = `
                    <div class="parameter-group">
                        <label for="mood-target">Target Average Mood:</label>
                        <select id="mood-target">
                            <option value="1">Sad or Better</option>
                            <option value="2">Okay or Better</option>
                            <option value="3" selected>Happy or Better</option>
                            <option value="4">Very Happy</option>
                        </select>
                    </div>
                `;
                break;
                
            case 'custom':
                parameters.innerHTML = `
                    <div class="parameter-group">
                        <label for="custom-metric">How will you measure progress?</label>
                        <input type="text" id="custom-metric" placeholder="e.g., Days without anxiety, Books read, etc.">
                    </div>
                    <div class="parameter-group">
                        <label for="custom-target">Target Value:</label>
                        <input type="number" id="custom-target" min="1" value="1">
                    </div>
                `;
                break;
        }
    }

    hideGoalForm() {
        document.getElementById('goal-form').classList.add('hidden');
        document.querySelectorAll('.goal-type-card').forEach(c => c.classList.remove('selected'));
        this.selectedGoalType = null;
        this.clearForm();
    }

    clearForm() {
        document.getElementById('goal-title').value = '';
        document.getElementById('goal-description').value = '';
        document.getElementById('goal-deadline').value = '';
    }

    createGoal() {
        const title = document.getElementById('goal-title').value.trim();
        const description = document.getElementById('goal-description').value.trim();
        const deadline = document.getElementById('goal-deadline').value;

        if (!title || !description || !deadline) {
            alert('Please fill in all required fields.');
            return;
        }

        const goal = {
            id: this.generateGoalId(),
            title,
            description,
            type: this.selectedGoalType,
            deadline,
            createdDate: getCurrentDate(),
            completed: false,
            completedDate: null
        };

        // Add type-specific properties
        switch (this.selectedGoalType) {
            case 'streak':
                goal.target = parseInt(document.getElementById('streak-target').value);
                break;
            case 'activity':
                goal.activity = document.getElementById('activity-type').value;
                goal.target = parseInt(document.getElementById('activity-target').value);
                break;
            case 'mood':
                goal.target = parseInt(document.getElementById('mood-target').value);
                break;
            case 'custom':
                goal.metric = document.getElementById('custom-metric').value;
                goal.target = parseInt(document.getElementById('custom-target').value);
                goal.progress = 0;
                break;
        }

        this.userData.goals.push(goal);
        saveUserData(this.userData);
        
        this.loadGoals();
        this.hideGoalForm();
        
        // Show success message
        this.showSuccessMessage('Goal created successfully!');
    }

    async suggestGoalWithAI() {
        if (!openAI || !openAI.isConfigured) {
            alert('AI features are not configured. Please set up your OpenAI API key in Settings.');
            return;
        }

        const btn = document.getElementById('ai-suggest-goal');
        btn.textContent = 'Generating...';
        btn.disabled = true;

        try {
            const prompt = `Based on this user's wellness data, suggest a personalized goal:
            - Level ${this.userData.level}, ${this.userData.xp} XP
            - ${this.userData.streak} day streak
            - ${this.userData.stats.totalBreathingSessions} breathing sessions
            - ${this.userData.stats.totalJournalEntries} journal entries
            - ${this.userData.stats.totalMoodEntries} mood entries
            - ${this.userData.stats.totalMeditationMinutes} meditation minutes
            
            Suggest a specific, achievable goal with:
            1. A clear title (max 50 characters)
            2. A detailed description (max 200 characters)
            3. Recommended timeframe
            
            Format as: TITLE|DESCRIPTION|DAYS`;

            const suggestion = await openAI.makeAPICall([
                { role: 'system', content: 'You are a wellness coach. Suggest specific, achievable goals based on user data.' },
                { role: 'user', content: prompt }
            ]);

            const parts = suggestion.split('|');
            if (parts.length >= 3) {
                document.getElementById('goal-title').value = parts[0].trim();
                document.getElementById('goal-description').value = parts[1].trim();
                
                const days = parseInt(parts[2].trim()) || 7;
                document.getElementById('goal-deadline').value = this.getDateInDays(days);
            }

        } catch (error) {
            console.error('Error generating AI suggestion:', error);
            alert('Unable to generate AI suggestion. Please try again later.');
        }

        btn.textContent = '🤖 AI Suggest';
        btn.disabled = false;
    }

    useTemplate(templateId) {
        const template = this.goalTemplates[templateId];
        if (!template) return;

        // Select the appropriate goal type
        const typeCard = document.querySelector(`[data-type="${template.type}"]`);
        if (typeCard) {
            typeCard.click();
        }

        // Fill in the form
        setTimeout(() => {
            document.getElementById('goal-title').value = template.title;
            document.getElementById('goal-description').value = template.description;
            document.getElementById('goal-deadline').value = template.deadline;

            // Fill type-specific fields
            switch (template.type) {
                case 'streak':
                    if (document.getElementById('streak-target')) {
                        document.getElementById('streak-target').value = template.target;
                    }
                    break;
                case 'activity':
                    if (document.getElementById('activity-type')) {
                        document.getElementById('activity-type').value = template.activity;
                    }
                    if (document.getElementById('activity-target')) {
                        document.getElementById('activity-target').value = template.target;
                    }
                    break;
                case 'mood':
                    if (document.getElementById('mood-target')) {
                        document.getElementById('mood-target').value = template.target;
                    }
                    break;
            }
        }, 100);
    }

    completeGoal(goalId) {
        const goal = this.userData.goals.find(g => g.id === goalId);
        if (goal) {
            goal.completed = true;
            goal.completedDate = getCurrentDate();
            
            // Award bonus XP for goal completion
            addXP(50);
            
            saveUserData(this.userData);
            this.loadGoals();
            
            this.showSuccessMessage('🎉 Goal completed! +50 XP earned!');
            showConfetti();
        }
    }

    deleteGoal(goalId) {
        if (confirm('Are you sure you want to delete this goal?')) {
            this.userData.goals = this.userData.goals.filter(g => g.id !== goalId);
            saveUserData(this.userData);
            this.loadGoals();
            this.showSuccessMessage('Goal deleted.');
        }
    }

    editGoal(goalId) {
        // For now, just allow deletion and recreation
        alert('Goal editing will be available in a future update. You can delete and recreate the goal for now.');
    }

    generateGoalId() {
        return 'goal_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    getDateInDays(days) {
        const date = new Date();
        date.setDate(date.getDate() + days);
        return date.toISOString().split('T')[0];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
        });
    }

    showSuccessMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'success-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #56ab2f;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            z-index: 1000;
            animation: slideInRight 0.5s ease-out;
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.5s ease-in forwards';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    document.body.removeChild(messageDiv);
                }
            }, 500);
        }, 3000);
    }
}

// Initialize goals manager
const goalsManager = new GoalsManager();

document.addEventListener('DOMContentLoaded', function() {
    goalsManager.initialize();
});
