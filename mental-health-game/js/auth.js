// Authentication and User Management System

class Auth {
    constructor() {
        this.currentUser = null;
        this.sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours
        this.init();
    }

    // Initialize authentication system
    init() {
        this.loadCurrentUser();
        this.setupSessionCheck();
    }

    // Load current user from storage
    loadCurrentUser() {
        try {
            const userData = localStorage.getItem('mindjourney_auth');
            if (userData) {
                const authData = JSON.parse(userData);
                
                // Check if session is still valid
                if (this.isSessionValid(authData)) {
                    this.currentUser = authData.user;
                    this.updateLastActivity();
                } else {
                    this.signOut();
                }
            }
        } catch (error) {
            console.error('Error loading user session:', error);
            this.signOut();
        }
    }

    // Check if session is still valid
    isSessionValid(authData) {
        if (!authData.lastActivity) return false;
        
        const now = Date.now();
        const lastActivity = new Date(authData.lastActivity).getTime();
        
        return (now - lastActivity) < this.sessionTimeout;
    }

    // Setup periodic session check
    setupSessionCheck() {
        setInterval(() => {
            if (this.currentUser) {
                const authData = this.getAuthData();
                if (!this.isSessionValid(authData)) {
                    this.signOut();
                    window.location.href = 'signin.html';
                }
            }
        }, 60000); // Check every minute
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Sign up new user
    async signUp(userData) {
        try {
            // Validate input
            const validation = this.validateSignUpData(userData);
            if (!validation.valid) {
                return { success: false, message: validation.message };
            }

            // Check if user already exists
            const existingUsers = this.getAllUsers();
            const emailExists = existingUsers.some(user => user.email === userData.email);
            
            if (emailExists) {
                return { success: false, message: 'An account with this email already exists.' };
            }

            // Create new user
            const newUser = {
                id: this.generateUserId(),
                email: userData.email,
                firstName: userData.firstName,
                lastName: userData.lastName,
                fullName: `${userData.firstName} ${userData.lastName}`,
                birthdate: userData.birthdate || null,
                newsletter: userData.newsletter || false,
                avatar: this.generateAvatar(userData.firstName),
                createdAt: new Date().toISOString(),
                emailVerified: false,
                isDemo: false
            };

            // Hash password (in a real app, this would be done server-side)
            const hashedPassword = await this.hashPassword(userData.password);
            
            // Save user credentials
            this.saveUserCredentials(newUser.email, hashedPassword);
            
            // Save user to users list
            this.saveUserToList(newUser);
            
            // Initialize user data
            const userProfile = initializeUser(newUser.fullName, newUser.avatar);
            userProfile.email = newUser.email;
            userProfile.firstName = newUser.firstName;
            userProfile.lastName = newUser.lastName;
            userProfile.birthdate = newUser.birthdate;
            userProfile.createdAt = newUser.createdAt;
            
            // Auto sign in
            this.currentUser = newUser;
            this.saveAuthSession(newUser);
            
            return { success: true, user: newUser };
            
        } catch (error) {
            console.error('Sign up error:', error);
            return { success: false, message: 'An error occurred during sign up.' };
        }
    }

    // Sign in existing user
    async signIn(email, password, remember = false) {
        try {
            // Get user credentials
            const credentials = this.getUserCredentials(email);
            if (!credentials) {
                return { success: false, message: 'Invalid email or password.' };
            }

            // Verify password
            const passwordValid = await this.verifyPassword(password, credentials.hashedPassword);
            if (!passwordValid) {
                return { success: false, message: 'Invalid email or password.' };
            }

            // Get user data
            const users = this.getAllUsers();
            const user = users.find(u => u.email === email);
            
            if (!user) {
                return { success: false, message: 'User account not found.' };
            }

            // Update last login
            user.lastLogin = new Date().toISOString();
            this.updateUserInList(user);

            // Set session
            this.currentUser = user;
            this.saveAuthSession(user, remember);
            
            // Update user data login
            updateDailyLogin();
            
            return { success: true, user: user };
            
        } catch (error) {
            console.error('Sign in error:', error);
            return { success: false, message: 'An error occurred during sign in.' };
        }
    }

    // Create demo user
    createDemoUser() {
        const demoUser = {
            id: 'demo_' + Date.now(),
            email: '<EMAIL>',
            firstName: 'Demo',
            lastName: 'User',
            fullName: 'Demo User',
            avatar: '🧘',
            createdAt: new Date().toISOString(),
            isDemo: true,
            emailVerified: true
        };

        // Initialize demo user data
        const userProfile = initializeUser(demoUser.fullName, demoUser.avatar);
        userProfile.email = demoUser.email;
        userProfile.isDemo = true;

        // Set as current user
        this.currentUser = demoUser;
        this.saveAuthSession(demoUser, false);

        return demoUser;
    }

    // Sign out user
    signOut() {
        this.currentUser = null;
        localStorage.removeItem('mindjourney_auth');
        
        // Clear user data if demo user
        const userData = getUserData();
        if (userData && userData.isDemo) {
            clearUserData();
        }
    }

    // Request password reset
    async requestPasswordReset(email) {
        try {
            const users = this.getAllUsers();
            const user = users.find(u => u.email === email);
            
            if (!user) {
                // Don't reveal if email exists for security
                return { success: true, message: 'If an account exists, a reset link has been sent.' };
            }

            // In a real app, this would send an email
            // For demo purposes, we'll just log it
            console.log(`Password reset requested for: ${email}`);
            
            return { success: true, message: 'Password reset link sent to your email.' };
            
        } catch (error) {
            console.error('Password reset error:', error);
            throw error;
        }
    }

    // Validate sign up data
    validateSignUpData(userData) {
        if (!userData.firstName || userData.firstName.trim().length < 2) {
            return { valid: false, message: 'First name must be at least 2 characters.' };
        }

        if (!userData.lastName || userData.lastName.trim().length < 2) {
            return { valid: false, message: 'Last name must be at least 2 characters.' };
        }

        if (!userData.email || !this.isValidEmail(userData.email)) {
            return { valid: false, message: 'Please enter a valid email address.' };
        }

        if (!userData.password || userData.password.length < 8) {
            return { valid: false, message: 'Password must be at least 8 characters.' };
        }

        if (userData.password !== userData.confirmPassword) {
            return { valid: false, message: 'Passwords do not match.' };
        }

        return { valid: true };
    }

    // Validate email format
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Generate user ID
    generateUserId() {
        return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Generate avatar based on name
    generateAvatar(firstName) {
        const avatars = ['🧘', '🌟', '🌸', '🦋', '🌈', '🍃', '💫', '🌺', '🕊️', '🌙'];
        const index = firstName.charCodeAt(0) % avatars.length;
        return avatars[index];
    }

    // Simple password hashing (in production, use proper bcrypt or similar)
    async hashPassword(password) {
        const encoder = new TextEncoder();
        const data = encoder.encode(password + 'mindjourney_salt');
        const hashBuffer = await crypto.subtle.digest('SHA-256', data);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    }

    // Verify password
    async verifyPassword(password, hashedPassword) {
        const inputHash = await this.hashPassword(password);
        return inputHash === hashedPassword;
    }

    // Save user credentials
    saveUserCredentials(email, hashedPassword) {
        try {
            const credentials = this.getAllCredentials();
            credentials[email] = { hashedPassword, createdAt: new Date().toISOString() };
            localStorage.setItem('mindjourney_credentials', JSON.stringify(credentials));
        } catch (error) {
            console.error('Error saving credentials:', error);
        }
    }

    // Get user credentials
    getUserCredentials(email) {
        try {
            const credentials = this.getAllCredentials();
            return credentials[email] || null;
        } catch (error) {
            console.error('Error getting credentials:', error);
            return null;
        }
    }

    // Get all credentials
    getAllCredentials() {
        try {
            const data = localStorage.getItem('mindjourney_credentials');
            return data ? JSON.parse(data) : {};
        } catch (error) {
            console.error('Error loading credentials:', error);
            return {};
        }
    }

    // Save user to users list
    saveUserToList(user) {
        try {
            const users = this.getAllUsers();
            users.push(user);
            localStorage.setItem('mindjourney_users', JSON.stringify(users));
        } catch (error) {
            console.error('Error saving user to list:', error);
        }
    }

    // Update user in list
    updateUserInList(updatedUser) {
        try {
            const users = this.getAllUsers();
            const index = users.findIndex(u => u.id === updatedUser.id);
            if (index !== -1) {
                users[index] = updatedUser;
                localStorage.setItem('mindjourney_users', JSON.stringify(users));
            }
        } catch (error) {
            console.error('Error updating user in list:', error);
        }
    }

    // Get all users
    getAllUsers() {
        try {
            const data = localStorage.getItem('mindjourney_users');
            return data ? JSON.parse(data) : [];
        } catch (error) {
            console.error('Error loading users:', error);
            return [];
        }
    }

    // Save authentication session
    saveAuthSession(user, remember = false) {
        try {
            const authData = {
                user: user,
                lastActivity: new Date().toISOString(),
                remember: remember,
                sessionStart: new Date().toISOString()
            };
            
            localStorage.setItem('mindjourney_auth', JSON.stringify(authData));
        } catch (error) {
            console.error('Error saving auth session:', error);
        }
    }

    // Get auth data
    getAuthData() {
        try {
            const data = localStorage.getItem('mindjourney_auth');
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error getting auth data:', error);
            return null;
        }
    }

    // Update last activity
    updateLastActivity() {
        try {
            const authData = this.getAuthData();
            if (authData) {
                authData.lastActivity = new Date().toISOString();
                localStorage.setItem('mindjourney_auth', JSON.stringify(authData));
            }
        } catch (error) {
            console.error('Error updating last activity:', error);
        }
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Check if user is demo user
    isDemoUser() {
        return this.currentUser && this.currentUser.isDemo;
    }

    // Get user profile data
    getUserProfile() {
        if (!this.currentUser) return null;
        
        const userData = getUserData();
        if (userData) {
            return {
                ...this.currentUser,
                ...userData
            };
        }
        
        return this.currentUser;
    }

    // Update user profile
    async updateUserProfile(updates) {
        if (!this.currentUser) {
            throw new Error('No user signed in');
        }

        try {
            // Update auth user data
            Object.assign(this.currentUser, updates);
            this.updateUserInList(this.currentUser);
            
            // Update session
            this.saveAuthSession(this.currentUser);
            
            // Update user data
            const userData = getUserData();
            if (userData) {
                Object.assign(userData, updates);
                saveUserData(userData);
            }
            
            return { success: true };
            
        } catch (error) {
            console.error('Error updating user profile:', error);
            return { success: false, message: 'Failed to update profile.' };
        }
    }

    // Delete user account
    async deleteAccount() {
        if (!this.currentUser) {
            throw new Error('No user signed in');
        }

        try {
            const email = this.currentUser.email;
            
            // Remove from users list
            const users = this.getAllUsers();
            const filteredUsers = users.filter(u => u.id !== this.currentUser.id);
            localStorage.setItem('mindjourney_users', JSON.stringify(filteredUsers));
            
            // Remove credentials
            const credentials = this.getAllCredentials();
            delete credentials[email];
            localStorage.setItem('mindjourney_credentials', JSON.stringify(credentials));
            
            // Clear user data
            clearUserData();
            
            // Sign out
            this.signOut();
            
            return { success: true };
            
        } catch (error) {
            console.error('Error deleting account:', error);
            return { success: false, message: 'Failed to delete account.' };
        }
    }

    // Export user data
    exportUserData() {
        if (!this.currentUser) return null;
        
        const userData = getUserData();
        const authData = this.getAuthData();
        
        return {
            profile: this.currentUser,
            wellness: userData,
            exported: new Date().toISOString()
        };
    }
}

// Create global Auth instance
window.Auth = new Auth();

// Activity tracking
document.addEventListener('click', () => {
    if (Auth.isAuthenticated()) {
        Auth.updateLastActivity();
    }
});

document.addEventListener('keypress', () => {
    if (Auth.isAuthenticated()) {
        Auth.updateLastActivity();
    }
});
