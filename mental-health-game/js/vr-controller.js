// VR Controller - Handles VR headset detection and WebXR functionality

class VRController {
    constructor() {
        this.isVRSupported = false;
        this.isVRActive = false;
        this.session = null;
        this.meditationTimer = null;
        this.currentDuration = 0;
        this.timeRemaining = 0;
        this.audioContext = null;
        this.ambientAudio = null;
        this.binauralBeats = null;
    }

    // Check if VR is supported by the browser and device
    static async checkVRSupport() {
        try {
            if (!navigator.xr) {
                console.log('WebXR not supported');
                return false;
            }

            const isSupported = await navigator.xr.isSessionSupported('immersive-vr');
            console.log('VR Support:', isSupported);
            return isSupported;
        } catch (error) {
            console.error('Error checking VR support:', error);
            return false;
        }
    }

    // Initialize VR controller
    async initialize() {
        this.isVRSupported = await VRController.checkVRSupport();
        
        if (this.isVRSupported) {
            this.setupVREventListeners();
        }

        // Initialize audio context for spatial audio
        this.initializeAudio();
        
        return this.isVRSupported;
    }

    // Setup VR-specific event listeners
    setupVREventListeners() {
        // Listen for VR session events
        if (navigator.xr) {
            navigator.xr.addEventListener('devicechange', () => {
                this.checkVRDeviceStatus();
            });
        }

        // Handle VR session end
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && this.isVRActive) {
                this.pauseMeditationSession();
            }
        });
    }

    // Check VR device status
    async checkVRDeviceStatus() {
        try {
            const isSupported = await navigator.xr.isSessionSupported('immersive-vr');
            if (isSupported !== this.isVRSupported) {
                this.isVRSupported = isSupported;
                this.notifyVRStatusChange();
            }
        } catch (error) {
            console.error('Error checking VR device status:', error);
        }
    }

    // Notify about VR status changes
    notifyVRStatusChange() {
        const notification = document.getElementById('vr-notification');
        if (this.isVRSupported && notification) {
            notification.classList.remove('hidden');
        }
    }

    // Enter VR mode
    async enterVR() {
        if (!this.isVRSupported) {
            throw new Error('VR not supported');
        }

        try {
            // Request VR session
            this.session = await navigator.xr.requestSession('immersive-vr', {
                requiredFeatures: ['local-floor'],
                optionalFeatures: ['hand-tracking', 'eye-tracking']
            });

            this.isVRActive = true;

            // Setup VR session
            await this.setupVRSession();

            // Notify Three.js scene about VR mode
            if (window.ThreeScene) {
                ThreeScene.enableVR(this.session);
            }

            console.log('VR mode activated');
            return true;

        } catch (error) {
            console.error('Failed to enter VR:', error);
            this.isVRActive = false;
            throw error;
        }
    }

    // Setup VR session with proper event handlers
    async setupVRSession() {
        if (!this.session) return;

        // Handle session end
        this.session.addEventListener('end', () => {
            this.isVRActive = false;
            this.session = null;
            console.log('VR session ended');
        });

        // Handle input sources (controllers)
        this.session.addEventListener('inputsourceschange', (event) => {
            this.handleInputSourcesChange(event);
        });

        // Setup reference space
        this.referenceSpace = await this.session.requestReferenceSpace('local-floor');
    }

    // Handle VR controller input changes
    handleInputSourcesChange(event) {
        // Handle added input sources (controllers)
        event.added.forEach(inputSource => {
            console.log('VR controller connected:', inputSource);
            this.setupControllerInteraction(inputSource);
        });

        // Handle removed input sources
        event.removed.forEach(inputSource => {
            console.log('VR controller disconnected:', inputSource);
        });
    }

    // Setup controller interaction for meditation controls
    setupControllerInteraction(inputSource) {
        if (inputSource.gamepad) {
            // Setup gamepad controls for meditation
            this.setupGamepadControls(inputSource.gamepad);
        }

        if (inputSource.hand) {
            // Setup hand tracking for gesture controls
            this.setupHandTracking(inputSource.hand);
        }
    }

    // Setup gamepad controls
    setupGamepadControls(gamepad) {
        // Map buttons to meditation functions
        // Button 0 (trigger): Start/pause meditation
        // Button 1 (grip): Change environment
        // Thumbstick: Navigate UI
    }

    // Setup hand tracking
    setupHandTracking(hand) {
        // Implement gesture recognition for meditation controls
        // Pinch gesture: Select options
        // Open palm: Pause/resume
        // Pointing: Navigate environments
    }

    // Initialize audio context for spatial audio
    initializeAudio() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            console.log('Audio context initialized');
        } catch (error) {
            console.error('Failed to initialize audio context:', error);
        }
    }

    // Start meditation session with VR enhancements
    async startMeditationSession(duration, settings) {
        this.currentDuration = duration;
        this.timeRemaining = duration;

        // Setup audio based on settings
        if (settings.ambientSounds) {
            await this.startAmbientAudio();
        }

        if (settings.binauralBeats) {
            await this.startBinauralBeats();
        }

        if (settings.guidedVoice) {
            await this.startGuidedVoice();
        }

        // Start meditation timer
        this.startMeditationTimer();

        // Enable VR-specific meditation features
        if (this.isVRActive) {
            this.enableVRMeditationFeatures();
        }

        // Update UI
        this.updateMeditationUI();

        console.log(`VR meditation session started: ${duration}s`);
    }

    // Start meditation timer
    startMeditationTimer() {
        this.meditationTimer = setInterval(() => {
            this.timeRemaining--;
            this.updateTimerDisplay();

            if (this.timeRemaining <= 0) {
                this.completeMeditationSession();
            }
        }, 1000);
    }

    // Update timer display
    updateTimerDisplay() {
        const minutes = Math.floor(this.timeRemaining / 60);
        const seconds = this.timeRemaining % 60;
        const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const timerDisplay = document.getElementById('vr-time-display');
        if (timerDisplay) {
            timerDisplay.textContent = timeString;
        }

        // Update breathing guide based on time
        this.updateBreathingGuide();
    }

    // Update breathing guide
    updateBreathingGuide() {
        const breathingText = document.getElementById('breathing-text');
        const breathingOrb = document.getElementById('breathing-orb');
        
        if (!breathingText || !breathingOrb) return;

        // Create breathing rhythm (4-4-6 pattern)
        const cycleTime = 14; // seconds per cycle
        const currentCycle = this.timeRemaining % cycleTime;

        if (currentCycle <= 4) {
            breathingText.textContent = 'Breathe In';
            breathingOrb.style.animationDuration = '4s';
        } else if (currentCycle <= 8) {
            breathingText.textContent = 'Hold';
            breathingOrb.style.animationDuration = '4s';
        } else {
            breathingText.textContent = 'Breathe Out';
            breathingOrb.style.animationDuration = '6s';
        }
    }

    // Enable VR-specific meditation features
    enableVRMeditationFeatures() {
        // Enable spatial audio
        if (this.audioContext && this.ambientAudio) {
            this.setupSpatialAudio();
        }

        // Enable haptic feedback for breathing guide
        this.enableHapticBreathing();

        // Enable eye tracking for focus measurement (if available)
        this.enableEyeTracking();
    }

    // Setup spatial audio for immersive experience
    setupSpatialAudio() {
        if (!this.audioContext || !this.ambientAudio) return;

        try {
            // Create spatial audio nodes
            const panner = this.audioContext.createPanner();
            panner.panningModel = 'HRTF';
            panner.distanceModel = 'inverse';
            panner.refDistance = 1;
            panner.maxDistance = 10000;
            panner.rolloffFactor = 1;
            panner.coneInnerAngle = 360;
            panner.coneOuterAngle = 0;
            panner.coneOuterGain = 0;

            // Connect audio nodes
            this.ambientAudio.connect(panner);
            panner.connect(this.audioContext.destination);

            console.log('Spatial audio enabled');
        } catch (error) {
            console.error('Failed to setup spatial audio:', error);
        }
    }

    // Enable haptic feedback for breathing guide
    enableHapticBreathing() {
        if (!this.session) return;

        // Use controller haptics to guide breathing
        this.session.inputSources.forEach(inputSource => {
            if (inputSource.gamepad && inputSource.gamepad.hapticActuators) {
                this.setupBreathingHaptics(inputSource.gamepad.hapticActuators[0]);
            }
        });
    }

    // Setup breathing haptics
    setupBreathingHaptics(hapticActuator) {
        if (!hapticActuator) return;

        // Create breathing rhythm with haptic feedback
        const breathingInterval = setInterval(() => {
            if (!this.isVRActive || this.timeRemaining <= 0) {
                clearInterval(breathingInterval);
                return;
            }

            // Gentle pulse for breathing rhythm
            hapticActuator.pulse(0.3, 100); // 30% intensity, 100ms duration
        }, 4000); // Every 4 seconds
    }

    // Enable eye tracking for focus measurement
    enableEyeTracking() {
        // Implementation for eye tracking if supported
        // This would measure focus and attention during meditation
    }

    // Start ambient audio
    async startAmbientAudio() {
        if (!this.audioContext) return;

        try {
            // Load ambient sound based on selected environment
            const environment = document.querySelector('.env-btn.active')?.dataset.env || 'forest';
            const audioUrl = this.getAmbientAudioUrl(environment);
            
            const response = await fetch(audioUrl);
            const arrayBuffer = await response.arrayBuffer();
            const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

            this.ambientAudio = this.audioContext.createBufferSource();
            this.ambientAudio.buffer = audioBuffer;
            this.ambientAudio.loop = true;
            this.ambientAudio.connect(this.audioContext.destination);
            this.ambientAudio.start();

            console.log('Ambient audio started');
        } catch (error) {
            console.error('Failed to start ambient audio:', error);
        }
    }

    // Get ambient audio URL based on environment
    getAmbientAudioUrl(environment) {
        const audioMap = {
            forest: 'assets/sounds/forest-ambient.mp3',
            beach: 'assets/sounds/ocean-waves.mp3',
            mountain: 'assets/sounds/mountain-wind.mp3',
            space: 'assets/sounds/space-ambient.mp3'
        };
        return audioMap[environment] || audioMap.forest;
    }

    // Start binaural beats
    async startBinauralBeats() {
        if (!this.audioContext) return;

        try {
            // Create binaural beats (e.g., 40Hz base with 6Hz difference for theta waves)
            const leftOscillator = this.audioContext.createOscillator();
            const rightOscillator = this.audioContext.createOscillator();
            const leftGain = this.audioContext.createGain();
            const rightGain = this.audioContext.createGain();
            const merger = this.audioContext.createChannelMerger(2);

            leftOscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
            rightOscillator.frequency.setValueAtTime(206, this.audioContext.currentTime); // 6Hz difference

            leftGain.gain.setValueAtTime(0.1, this.audioContext.currentTime);
            rightGain.gain.setValueAtTime(0.1, this.audioContext.currentTime);

            leftOscillator.connect(leftGain);
            rightOscillator.connect(rightGain);
            leftGain.connect(merger, 0, 0);
            rightGain.connect(merger, 0, 1);
            merger.connect(this.audioContext.destination);

            leftOscillator.start();
            rightOscillator.start();

            this.binauralBeats = { leftOscillator, rightOscillator, leftGain, rightGain };
            console.log('Binaural beats started');
        } catch (error) {
            console.error('Failed to start binaural beats:', error);
        }
    }

    // Start guided voice
    async startGuidedVoice() {
        // Implementation for guided meditation voice
        // This would use text-to-speech or pre-recorded audio
        console.log('Guided voice meditation started');
    }

    // Update meditation UI
    updateMeditationUI() {
        const phaseElement = document.getElementById('meditation-phase');
        if (phaseElement) {
            const phases = [
                'Settling into your space...',
                'Focus on your breath...',
                'Let thoughts pass by...',
                'Deepen your relaxation...',
                'Embrace the stillness...'
            ];
            
            const phaseIndex = Math.floor((this.currentDuration - this.timeRemaining) / (this.currentDuration / phases.length));
            phaseElement.textContent = phases[Math.min(phaseIndex, phases.length - 1)];
        }
    }

    // Pause meditation session
    pauseMeditationSession() {
        if (this.meditationTimer) {
            clearInterval(this.meditationTimer);
            this.meditationTimer = null;
        }

        // Pause audio
        if (this.ambientAudio) {
            this.ambientAudio.disconnect();
        }

        console.log('Meditation session paused');
    }

    // Resume meditation session
    resumeMeditationSession() {
        if (!this.meditationTimer && this.timeRemaining > 0) {
            this.startMeditationTimer();
        }

        // Resume audio
        if (this.ambientAudio && this.audioContext) {
            this.ambientAudio.connect(this.audioContext.destination);
        }

        console.log('Meditation session resumed');
    }

    // Stop meditation session
    stopMeditationSession() {
        if (this.meditationTimer) {
            clearInterval(this.meditationTimer);
            this.meditationTimer = null;
        }

        this.stopAllAudio();
        this.timeRemaining = 0;

        console.log('Meditation session stopped');
    }

    // Complete meditation session
    completeMeditationSession() {
        this.stopMeditationSession();
        
        // Calculate session stats
        const completedDuration = this.currentDuration;
        const sessionStats = {
            duration: completedDuration,
            environment: document.querySelector('.env-btn.active')?.dataset.env || 'forest',
            vrMode: this.isVRActive
        };

        // Save session data
        this.saveMeditationSession(sessionStats);

        // Show completion UI
        this.showSessionComplete(sessionStats);

        console.log('Meditation session completed:', sessionStats);
    }

    // Stop all audio
    stopAllAudio() {
        if (this.ambientAudio) {
            this.ambientAudio.stop();
            this.ambientAudio = null;
        }

        if (this.binauralBeats) {
            this.binauralBeats.leftOscillator.stop();
            this.binauralBeats.rightOscillator.stop();
            this.binauralBeats = null;
        }
    }

    // Save meditation session data
    saveMeditationSession(sessionStats) {
        try {
            const userData = getUserData();
            if (!userData.vrMeditation) {
                userData.vrMeditation = [];
            }

            userData.vrMeditation.push({
                ...sessionStats,
                timestamp: new Date().toISOString(),
                xpEarned: 30 // VR meditation gives more XP
            });

            saveUserData(userData);
        } catch (error) {
            console.error('Failed to save meditation session:', error);
        }
    }

    // Show session complete
    showSessionComplete(sessionStats) {
        const sessionComplete = document.getElementById('session-complete');
        const durationDisplay = document.getElementById('session-duration');
        
        if (sessionComplete) {
            sessionComplete.classList.remove('hidden');
        }

        if (durationDisplay) {
            const minutes = Math.floor(sessionStats.duration / 60);
            const seconds = sessionStats.duration % 60;
            durationDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // Exit VR mode
    async exitVR() {
        if (this.session) {
            await this.session.end();
        }
        this.isVRActive = false;
        this.session = null;
        console.log('Exited VR mode');
    }
}

// Create global VR controller instance
window.VRController = new VRController();
