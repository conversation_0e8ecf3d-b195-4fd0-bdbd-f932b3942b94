// Nutrition Tracker for MindJourney Enhanced

class NutritionTracker {
    constructor() {
        this.userData = null;
        this.currentEntry = {
            water: 0,
            vegetables: 0,
            fruits: 0,
            grains: 0,
            protein: 0,
            moodFoods: [],
            energy: null,
            notes: ''
        };
        this.selectedEnergy = null;
    }

    initialize() {
        this.userData = getUserData();
        if (!this.userData) {
            window.location.href = 'index.html';
            return;
        }

        // Initialize nutrition data if it doesn't exist
        if (!this.userData.nutritionData) {
            this.userData.nutritionData = [];
            saveUserData(this.userData);
        }

        this.setupEventListeners();
        this.loadTodaysEntry();
        this.updateStatistics();
        this.displayNutritionHistory();
        this.renderWaterGlasses();
        
        // Set today's date as default
        document.getElementById('nutrition-date').value = getCurrentDate();
    }

    setupEventListeners() {
        // Water tracking
        document.getElementById('add-water').addEventListener('click', () => {
            this.currentEntry.water = Math.min(this.currentEntry.water + 1, 12);
            this.updateWaterDisplay();
            this.renderWaterGlasses();
        });

        document.getElementById('remove-water').addEventListener('click', () => {
            this.currentEntry.water = Math.max(this.currentEntry.water - 1, 0);
            this.updateWaterDisplay();
            this.renderWaterGlasses();
        });

        // Serving buttons
        document.querySelectorAll('.serving-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const type = btn.dataset.type;
                this.currentEntry[type] = Math.min(this.currentEntry[type] + 1, 10);
                this.updateServingDisplay(type);
            });
        });

        // Energy selector
        document.querySelectorAll('.energy-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.energy-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                this.selectedEnergy = parseInt(option.dataset.energy);
                this.currentEntry.energy = this.selectedEnergy;
            });
        });

        // Save button
        document.getElementById('save-nutrition').addEventListener('click', () => {
            this.saveNutritionEntry();
        });

        // AI analysis button
        document.getElementById('analyze-nutrition').addEventListener('click', () => {
            this.generateNutritionAnalysis();
        });

        // Date change
        document.getElementById('nutrition-date').addEventListener('change', () => {
            this.loadEntryForDate();
        });
    }

    loadTodaysEntry() {
        const today = getCurrentDate();
        const existingEntry = this.userData.nutritionData.find(entry => entry.date === today);
        
        if (existingEntry) {
            this.currentEntry = { ...existingEntry };
            this.selectedEnergy = existingEntry.energy;
            this.populateForm();
        } else {
            this.resetCurrentEntry();
        }
    }

    loadEntryForDate() {
        const selectedDate = document.getElementById('nutrition-date').value;
        const existingEntry = this.userData.nutritionData.find(entry => entry.date === selectedDate);
        
        if (existingEntry) {
            this.currentEntry = { ...existingEntry };
            this.selectedEnergy = existingEntry.energy;
            this.populateForm();
        } else {
            this.resetCurrentEntry();
        }
    }

    populateForm() {
        // Update water display
        this.updateWaterDisplay();
        this.renderWaterGlasses();

        // Update serving displays
        this.updateServingDisplay('vegetables');
        this.updateServingDisplay('fruits');
        this.updateServingDisplay('grains');
        this.updateServingDisplay('protein');

        // Update mood foods
        document.querySelectorAll('input[name="mood-foods"]').forEach(checkbox => {
            checkbox.checked = this.currentEntry.moodFoods.includes(checkbox.value);
        });

        // Update energy selection
        if (this.selectedEnergy) {
            document.querySelectorAll('.energy-option').forEach(opt => opt.classList.remove('selected'));
            document.querySelector(`[data-energy="${this.selectedEnergy}"]`)?.classList.add('selected');
        }

        // Update notes
        document.getElementById('nutrition-notes').value = this.currentEntry.notes || '';
    }

    resetCurrentEntry() {
        this.currentEntry = {
            water: 0,
            vegetables: 0,
            fruits: 0,
            grains: 0,
            protein: 0,
            moodFoods: [],
            energy: null,
            notes: ''
        };
        this.selectedEnergy = null;
        this.populateForm();
    }

    updateWaterDisplay() {
        document.getElementById('water-count').textContent = this.currentEntry.water;
    }

    updateServingDisplay(type) {
        const countElement = document.getElementById(`${type === 'vegetables' ? 'veg' : type === 'fruits' ? 'fruit' : type === 'grains' ? 'grain' : 'protein'}-count`);
        if (countElement) {
            countElement.textContent = this.currentEntry[type];
        }
    }

    renderWaterGlasses() {
        const container = document.getElementById('water-glasses');
        container.innerHTML = '';
        
        for (let i = 0; i < 8; i++) {
            const glass = document.createElement('div');
            glass.className = `water-glass ${i < this.currentEntry.water ? 'filled' : ''}`;
            container.appendChild(glass);
        }
    }

    saveNutritionEntry() {
        const selectedDate = document.getElementById('nutrition-date').value;
        const notes = document.getElementById('nutrition-notes').value;

        // Get selected mood foods
        const moodFoods = Array.from(document.querySelectorAll('input[name="mood-foods"]:checked'))
            .map(checkbox => checkbox.value);

        const nutritionEntry = {
            id: 'nutrition_' + Date.now(),
            date: selectedDate,
            water: this.currentEntry.water,
            vegetables: this.currentEntry.vegetables,
            fruits: this.currentEntry.fruits,
            grains: this.currentEntry.grains,
            protein: this.currentEntry.protein,
            moodFoods,
            energy: this.selectedEnergy,
            notes,
            timestamp: new Date().toISOString()
        };

        // Remove existing entry for this date if it exists
        this.userData.nutritionData = this.userData.nutritionData.filter(entry => entry.date !== selectedDate);
        
        // Add new entry
        this.userData.nutritionData.push(nutritionEntry);
        
        // Update stats
        if (!this.userData.stats.nutritionEntries) {
            this.userData.stats.nutritionEntries = 0;
        }
        this.userData.stats.nutritionEntries++;

        // Award XP for logging nutrition
        addXP(10);
        
        saveUserData(this.userData);
        
        // Update displays
        this.updateStatistics();
        this.displayNutritionHistory();
        
        // Show success message
        this.showSuccessMessage('Nutrition entry saved successfully! +10 XP earned!');
    }

    updateStatistics() {
        const nutritionData = this.userData.nutritionData;
        
        if (nutritionData.length === 0) {
            document.getElementById('avg-water').textContent = '0';
            document.getElementById('avg-vegetables').textContent = '0';
            document.getElementById('avg-energy').textContent = '0.0';
            document.getElementById('nutrition-streak').textContent = '0';
            return;
        }

        // Calculate averages
        const avgWater = nutritionData.reduce((sum, entry) => sum + entry.water, 0) / nutritionData.length;
        const avgVegetables = nutritionData.reduce((sum, entry) => sum + entry.vegetables, 0) / nutritionData.length;
        const energyEntries = nutritionData.filter(entry => entry.energy !== null);
        const avgEnergy = energyEntries.length > 0 
            ? energyEntries.reduce((sum, entry) => sum + entry.energy, 0) / energyEntries.length 
            : 0;

        // Calculate streak
        const streak = this.calculateNutritionStreak();

        // Update display
        document.getElementById('avg-water').textContent = avgWater.toFixed(1);
        document.getElementById('avg-vegetables').textContent = avgVegetables.toFixed(1);
        document.getElementById('avg-energy').textContent = avgEnergy.toFixed(1);
        document.getElementById('nutrition-streak').textContent = streak;
    }

    calculateNutritionStreak() {
        const sortedEntries = this.userData.nutritionData
            .sort((a, b) => new Date(b.date) - new Date(a.date));
        
        let streak = 0;
        let currentDate = new Date();
        
        for (const entry of sortedEntries) {
            const entryDate = new Date(entry.date);
            const daysDiff = Math.floor((currentDate - entryDate) / (1000 * 60 * 60 * 24));
            
            if (daysDiff === streak) {
                streak++;
                currentDate = entryDate;
            } else {
                break;
            }
        }
        
        return streak;
    }

    displayNutritionHistory() {
        const historyContainer = document.getElementById('nutrition-history');
        const nutritionData = this.userData.nutritionData.slice(-10).reverse(); // Last 10 entries, newest first

        if (nutritionData.length === 0) {
            historyContainer.innerHTML = '<div class="no-entries"><p>No nutrition entries yet. Start tracking your nutrition to see your history!</p></div>';
            return;
        }

        const historyHTML = nutritionData.map(entry => `
            <div class="nutrition-entry">
                <div class="nutrition-entry-info">
                    <div class="nutrition-entry-date">${this.formatDate(entry.date)}</div>
                    <div class="nutrition-entry-summary">
                        💧 ${entry.water} glasses • 🥬 ${entry.vegetables} veg • 🍎 ${entry.fruits} fruits
                        ${entry.moodFoods.length > 0 ? `• ${entry.moodFoods.join(', ')}` : ''}
                    </div>
                    ${entry.notes ? `<div class="nutrition-entry-notes">"${entry.notes}"</div>` : ''}
                </div>
                <div class="nutrition-entry-energy">
                    ${entry.energy ? `${this.getEnergyEmoji(entry.energy)} ${entry.energy}/5` : '⚡ --'}
                </div>
            </div>
        `).join('');

        historyContainer.innerHTML = historyHTML;
    }

    async generateNutritionAnalysis() {
        const analysisContainer = document.getElementById('nutrition-ai-analysis');
        
        if (!isAIAvailable()) {
            analysisContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <p>AI analysis requires OpenAI configuration. Please set up your API key in Settings to get personalized nutrition insights.</p>
                </div>
            `;
            return;
        }

        this.showLoading(analysisContainer, 'Analyzing your nutrition patterns...');

        try {
            const nutritionData = this.userData.nutritionData.slice(-30); // Last 30 days
            const nutritionSummary = this.prepareNutritionSummary(nutritionData);

            const prompt = `Analyze this user's nutrition patterns and their impact on mental health:

Nutrition Data Summary:
${nutritionSummary}

User Context:
- Name: ${this.userData.username}
- Wellness Level: ${this.userData.level}
- Current Streak: ${this.userData.streak} days

Provide analysis in this format:
1. HYDRATION PATTERNS: Assessment of water intake consistency
2. NUTRITION BALANCE: Evaluation of fruits, vegetables, and other food groups
3. MOOD-FOOD CONNECTIONS: How different foods correlate with energy levels
4. ENERGY TRENDS: Patterns in energy levels and potential causes
5. RECOMMENDATIONS: 3 specific, actionable nutrition improvements for mental health
6. MEAL TIMING: Suggestions for optimal eating patterns

Focus on mental health benefits and be encouraging and specific.`;

            const analysis = await openAI.makeAPICall([
                { role: 'system', content: getPersonalityPrompt() + ' You are analyzing nutrition patterns for mental wellness optimization.' },
                { role: 'user', content: prompt }
            ]);

            this.displayNutritionAnalysis(analysisContainer, analysis);

        } catch (error) {
            console.error('Error generating nutrition analysis:', error);
            analysisContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <p>Unable to generate nutrition analysis at this time. Please try again later.</p>
                </div>
            `;
        }
    }

    prepareNutritionSummary(nutritionData) {
        if (nutritionData.length === 0) {
            return 'No nutrition data available for analysis.';
        }

        const avgWater = nutritionData.reduce((sum, entry) => sum + entry.water, 0) / nutritionData.length;
        const avgVegetables = nutritionData.reduce((sum, entry) => sum + entry.vegetables, 0) / nutritionData.length;
        const avgFruits = nutritionData.reduce((sum, entry) => sum + entry.fruits, 0) / nutritionData.length;
        
        const energyEntries = nutritionData.filter(entry => entry.energy !== null);
        const avgEnergy = energyEntries.length > 0 
            ? energyEntries.reduce((sum, entry) => sum + entry.energy, 0) / energyEntries.length 
            : 0;

        const moodFoodCounts = {};
        nutritionData.forEach(entry => {
            entry.moodFoods.forEach(food => {
                moodFoodCounts[food] = (moodFoodCounts[food] || 0) + 1;
            });
        });

        const commonMoodFoods = Object.entries(moodFoodCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([food, count]) => `${food} (${count} times)`);

        return `
Total Entries: ${nutritionData.length}
Average Water: ${avgWater.toFixed(1)} glasses/day
Average Vegetables: ${avgVegetables.toFixed(1)} servings/day
Average Fruits: ${avgFruits.toFixed(1)} servings/day
Average Energy Level: ${avgEnergy.toFixed(1)}/5
Most Common Mood Foods: ${commonMoodFoods.join(', ') || 'None'}
Date Range: ${nutritionData[0].date} to ${nutritionData[nutritionData.length - 1].date}
        `.trim();
    }

    displayNutritionAnalysis(container, analysis) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>🧠 AI Nutrition Analysis</h4>
                <div class="analysis-content">${this.formatAnalysisText(analysis)}</div>
            </div>
        `;
    }

    formatAnalysisText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/(\d+\.\s*[A-Z\s]+:)/g, '<strong>$1</strong>')
            .replace(/([A-Z\s]+:)/g, '<strong>$1</strong>');
    }

    showLoading(container, message) {
        container.innerHTML = `
            <div class="analysis-loading">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    }

    getEnergyEmoji(energy) {
        const emojis = ['😴', '😐', '🙂', '😊', '🤩'];
        return emojis[energy - 1] || '😐';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
    }

    showSuccessMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'success-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #56ab2f;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            z-index: 1000;
            animation: slideInRight 0.5s ease-out;
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.5s ease-in forwards';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    document.body.removeChild(messageDiv);
                }
            }, 500);
        }, 3000);
    }
}

// Initialize nutrition tracker
const nutritionTracker = new NutritionTracker();

document.addEventListener('DOMContentLoaded', function() {
    nutritionTracker.initialize();
});
