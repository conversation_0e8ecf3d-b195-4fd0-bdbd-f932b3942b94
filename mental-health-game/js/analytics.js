// Analytics Engine for MindJourney

class AnalyticsEngine {
    constructor() {
        this.userData = null;
        this.moodChart = null;
    }

    initialize() {
        this.userData = getUserData();
        if (!this.userData) {
            window.location.href = 'index.html';
            return;
        }

        this.updateMoodTrends();
        this.updateActivityPatterns();
        this.generateHeatmap();
        this.updateStreakAnalysis();
        this.checkAIInsights();
        this.setupEventListeners();
    }

    updateMoodTrends() {
        const moodHistory = this.userData.moodHistory || [];
        
        if (moodHistory.length === 0) {
            document.getElementById('avg-mood').textContent = 'No data';
            document.getElementById('best-day').textContent = 'No data';
            document.getElementById('mood-trend').textContent = 'No data';
            return;
        }

        // Calculate average mood
        const avgMood = moodHistory.reduce((sum, entry) => sum + entry.mood, 0) / moodHistory.length;
        document.getElementById('avg-mood').textContent = this.getMoodLabel(Math.round(avgMood));

        // Find best day
        const bestMood = moodHistory.reduce((best, entry) => 
            entry.mood > best.mood ? entry : best
        );
        document.getElementById('best-day').textContent = this.formatDate(bestMood.date);

        // Calculate trend
        const recentMoods = moodHistory.slice(-7); // Last 7 entries
        const trend = this.calculateTrend(recentMoods.map(entry => entry.mood));
        document.getElementById('mood-trend').textContent = trend;

        // Draw mood chart
        this.drawMoodChart(moodHistory);
    }

    updateActivityPatterns() {
        const stats = this.userData.stats;
        
        // Update numbers
        document.getElementById('breathing-total').textContent = stats.totalBreathingSessions;
        document.getElementById('journal-total').textContent = stats.totalJournalEntries;
        document.getElementById('meditation-total').textContent = stats.totalMeditationMinutes;
        document.getElementById('mood-total').textContent = stats.totalMoodEntries;

        // Update progress bars (relative to goals)
        const goals = { breathing: 30, journal: 20, meditation: 120, mood: 30 };
        
        Object.keys(goals).forEach(activity => {
            const progress = Math.min((stats[`total${this.capitalize(activity)}${activity === 'meditation' ? 'Minutes' : activity === 'breathing' ? 'Sessions' : 'Entries'}`] / goals[activity]) * 100, 100);
            document.getElementById(`${activity}-progress`).style.width = progress + '%';
        });
    }

    generateHeatmap() {
        const heatmapGrid = document.getElementById('heatmap-grid');
        const today = new Date();
        const startDate = new Date(today.getTime() - (6 * 7 * 24 * 60 * 60 * 1000)); // 6 weeks ago

        heatmapGrid.innerHTML = '';

        for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
            const date = new Date(startDate.getTime() + (i * 24 * 60 * 60 * 1000));
            const dateStr = date.toISOString().split('T')[0];
            
            const dayElement = document.createElement('div');
            dayElement.className = 'heatmap-day';
            dayElement.title = this.formatDate(dateStr);
            
            // Calculate activity level for this day
            const activityLevel = this.getActivityLevel(dateStr);
            dayElement.classList.add(`level-${activityLevel}`);
            
            heatmapGrid.appendChild(dayElement);
        }
    }

    getActivityLevel(dateStr) {
        // Check if tasks were completed on this date
        let completedTasks = 0;
        
        Object.values(this.userData.dailyTasks).forEach(task => {
            if (task.date === dateStr && task.completed) {
                completedTasks++;
            }
        });

        // Return level 0-4 based on completed tasks
        return Math.min(completedTasks, 4);
    }

    updateStreakAnalysis() {
        document.getElementById('current-streak').textContent = `${this.userData.streak} days`;
        document.getElementById('longest-streak').textContent = `${this.userData.streak} days`; // Could track historical best
        document.getElementById('total-days').textContent = `${this.userData.totalDays} days`;
    }

    async checkAIInsights() {
        const statusIndicator = document.getElementById('ai-insights-status');
        const generateBtn = document.getElementById('generate-insights');
        
        if (openAI && openAI.isConfigured) {
            statusIndicator.className = 'ai-status-indicator enabled';
            statusIndicator.innerHTML = `
                <span class="status-text">🤖 AI insights ready</span>
                <span class="configure-link">Enhanced analytics active</span>
            `;
            generateBtn.disabled = false;
        } else {
            generateBtn.disabled = true;
        }
    }

    async generateAIInsights() {
        if (!openAI || !openAI.isConfigured) return;

        const generateBtn = document.getElementById('generate-insights');
        generateBtn.textContent = 'Generating Insights...';
        generateBtn.disabled = true;

        try {
            // Generate pattern analysis
            const patternPrompt = `Analyze this user's wellness data and provide insights about patterns:
            - Level ${this.userData.level}, ${this.userData.xp} XP
            - ${this.userData.streak} day streak
            - ${this.userData.stats.totalBreathingSessions} breathing sessions
            - ${this.userData.stats.totalJournalEntries} journal entries
            - ${this.userData.stats.totalMoodEntries} mood entries
            - ${this.userData.stats.totalMeditationMinutes} meditation minutes
            
            Provide a brief insight about their wellness patterns (2-3 sentences).`;

            const patternInsight = await openAI.makeAPICall([
                { role: 'system', content: 'You are a wellness analytics expert. Provide brief, encouraging insights about user patterns.' },
                { role: 'user', content: patternPrompt }
            ]);

            document.getElementById('pattern-insight').textContent = patternInsight;

            // Generate recommendations
            const recommendationPrompt = `Based on this wellness data, suggest 2-3 specific, actionable recommendations for improvement:
            Current activities: ${this.userData.stats.totalBreathingSessions} breathing, ${this.userData.stats.totalJournalEntries} journaling, ${this.userData.stats.totalMoodEntries} mood tracking, ${this.userData.stats.totalMeditationMinutes} meditation minutes.
            
            Provide practical, specific recommendations (2-3 sentences).`;

            const recommendations = await openAI.makeAPICall([
                { role: 'system', content: 'You are a wellness coach. Provide specific, actionable recommendations.' },
                { role: 'user', content: recommendationPrompt }
            ]);

            document.getElementById('recommendation-insight').textContent = recommendations;

            // Generate goals
            const goalPrompt = `Suggest 2-3 achievable wellness goals for the next week based on this data:
            Current level: ${this.userData.level}, Streak: ${this.userData.streak} days
            Recent activity levels and patterns suggest specific areas for growth.
            
            Provide encouraging, specific goals (2-3 sentences).`;

            const goals = await openAI.makeAPICall([
                { role: 'system', content: 'You are a wellness goal-setting expert. Suggest specific, achievable goals.' },
                { role: 'user', content: goalPrompt }
            ]);

            document.getElementById('goal-insight').textContent = goals;

        } catch (error) {
            console.error('Error generating AI insights:', error);
            document.getElementById('pattern-insight').textContent = 'Unable to generate insights at this time. Please try again later.';
        }

        generateBtn.textContent = 'Generate AI Insights';
        generateBtn.disabled = false;
    }

    drawMoodChart(moodHistory) {
        const canvas = document.getElementById('mood-chart');
        const ctx = canvas.getContext('2d');
        
        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        
        if (moodHistory.length === 0) {
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No mood data available', canvas.width / 2, canvas.height / 2);
            return;
        }

        // Prepare data (last 30 days)
        const recentMoods = moodHistory.slice(-30);
        const padding = 40;
        const chartWidth = canvas.width - (padding * 2);
        const chartHeight = canvas.height - (padding * 2);
        
        // Draw axes
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();

        // Draw mood line
        if (recentMoods.length > 1) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 2;
            ctx.beginPath();
            
            recentMoods.forEach((mood, index) => {
                const x = padding + (index / (recentMoods.length - 1)) * chartWidth;
                const y = canvas.height - padding - (mood.mood / 4) * chartHeight;
                
                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });
            
            ctx.stroke();
        }

        // Draw mood points
        recentMoods.forEach((mood, index) => {
            const x = padding + (index / Math.max(recentMoods.length - 1, 1)) * chartWidth;
            const y = canvas.height - padding - (mood.mood / 4) * chartHeight;
            
            ctx.fillStyle = this.getMoodColor(mood.mood);
            ctx.beginPath();
            ctx.arc(x, y, 4, 0, 2 * Math.PI);
            ctx.fill();
        });

        // Draw labels
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        
        // Y-axis labels
        for (let i = 0; i <= 4; i++) {
            const y = canvas.height - padding - (i / 4) * chartHeight;
            ctx.fillText(this.getMoodLabel(i), 5, y + 4);
        }
    }

    getMoodColor(moodValue) {
        const colors = ['#ff6b6b', '#ffa726', '#ffeb3b', '#66bb6a', '#4caf50'];
        return colors[moodValue] || '#ccc';
    }

    getMoodLabel(moodValue) {
        const labels = ['Very Sad', 'Sad', 'Okay', 'Happy', 'Very Happy'];
        return labels[moodValue] || 'Unknown';
    }

    calculateTrend(values) {
        if (values.length < 2) return 'Insufficient data';
        
        const first = values.slice(0, Math.ceil(values.length / 2));
        const second = values.slice(Math.floor(values.length / 2));
        
        const firstAvg = first.reduce((a, b) => a + b, 0) / first.length;
        const secondAvg = second.reduce((a, b) => a + b, 0) / second.length;
        
        const diff = secondAvg - firstAvg;
        
        if (diff > 0.3) return '📈 Improving';
        if (diff < -0.3) return '📉 Declining';
        return '➡️ Stable';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'short', 
            day: 'numeric' 
        });
    }

    capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }

    setupEventListeners() {
        document.getElementById('generate-insights').addEventListener('click', () => {
            this.generateAIInsights();
        });

        document.getElementById('export-csv').addEventListener('click', () => {
            this.exportAsCSV();
        });

        document.getElementById('export-json').addEventListener('click', () => {
            this.exportAsJSON();
        });

        document.getElementById('export-summary').addEventListener('click', () => {
            this.exportSummaryReport();
        });
    }

    exportAsCSV() {
        const data = [];
        
        // Add mood data
        this.userData.moodHistory.forEach(entry => {
            data.push({
                date: entry.date,
                type: 'mood',
                value: entry.mood,
                label: this.getMoodLabel(entry.mood)
            });
        });

        // Add journal data
        this.userData.journalEntries.forEach(entry => {
            data.push({
                date: entry.date,
                type: 'journal',
                value: entry.entry.length,
                label: 'Journal Entry'
            });
        });

        // Convert to CSV
        const csv = this.convertToCSV(data);
        this.downloadFile(csv, 'mindjourney-analytics.csv', 'text/csv');
    }

    exportAsJSON() {
        const analyticsData = {
            exportDate: new Date().toISOString(),
            userLevel: this.userData.level,
            userXP: this.userData.xp,
            streak: this.userData.streak,
            totalDays: this.userData.totalDays,
            stats: this.userData.stats,
            moodHistory: this.userData.moodHistory,
            journalEntries: this.userData.journalEntries.map(entry => ({
                date: entry.date,
                wordCount: entry.entry.split(' ').length
            })),
            badges: this.userData.badges
        };

        const json = JSON.stringify(analyticsData, null, 2);
        this.downloadFile(json, 'mindjourney-analytics.json', 'application/json');
    }

    exportSummaryReport() {
        const report = this.generateSummaryReport();
        this.downloadFile(report, 'mindjourney-summary.txt', 'text/plain');
    }

    generateSummaryReport() {
        const avgMood = this.userData.moodHistory.length > 0 
            ? this.userData.moodHistory.reduce((sum, entry) => sum + entry.mood, 0) / this.userData.moodHistory.length
            : 0;

        return `MindJourney Wellness Summary Report
Generated: ${new Date().toLocaleDateString()}

=== OVERVIEW ===
Level: ${this.userData.level}
Total XP: ${this.userData.xp}
Current Streak: ${this.userData.streak} days
Total Active Days: ${this.userData.totalDays}

=== ACTIVITY SUMMARY ===
Breathing Sessions: ${this.userData.stats.totalBreathingSessions}
Journal Entries: ${this.userData.stats.totalJournalEntries}
Mood Check-ins: ${this.userData.stats.totalMoodEntries}
Meditation Minutes: ${this.userData.stats.totalMeditationMinutes}

=== MOOD ANALYSIS ===
Average Mood: ${this.getMoodLabel(Math.round(avgMood))} (${avgMood.toFixed(1)}/4)
Total Mood Entries: ${this.userData.moodHistory.length}

=== ACHIEVEMENTS ===
Badges Earned: ${this.userData.badges.length}
${this.userData.badges.map(badgeId => `- ${getBadge(badgeId)?.name || badgeId}`).join('\n')}

=== NOTES ===
This report provides a snapshot of your mental wellness journey.
Continue your daily practices for optimal mental health benefits.
Remember: Progress, not perfection, is the goal.

Generated by MindJourney - Your Mental Wellness Companion`;
    }

    convertToCSV(data) {
        if (data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => headers.map(header => `"${row[header]}"`).join(','))
        ].join('\n');
        
        return csvContent;
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        link.click();
        
        URL.revokeObjectURL(url);
    }
}

// Initialize analytics when page loads
document.addEventListener('DOMContentLoaded', function() {
    const analytics = new AnalyticsEngine();
    analytics.initialize();
});
