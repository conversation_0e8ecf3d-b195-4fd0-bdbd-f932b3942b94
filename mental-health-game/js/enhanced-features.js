// Enhanced Features Module
// This file provides additional functionality for the MindJourney app

class EnhancedFeatures {
    constructor() {
        this.init();
    }

    init() {
        this.setupNotifications();
        this.setupKeyboardShortcuts();
        this.setupAccessibility();
        this.setupPerformanceOptimizations();
    }

    // Notification system
    setupNotifications() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
    }

    // Show notification
    showNotification(title, options = {}) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const notification = new Notification(title, {
                icon: '/assets/icon.png',
                badge: '/assets/badge.png',
                ...options
            });

            // Auto close after 5 seconds
            setTimeout(() => {
                notification.close();
            }, 5000);

            return notification;
        }
    }

    // Keyboard shortcuts
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K for quick search
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.openQuickSearch();
            }

            // Escape to close modals
            if (e.key === 'Escape') {
                this.closeAllModals();
            }

            // Alt + D for dashboard
            if (e.altKey && e.key === 'd') {
                e.preventDefault();
                window.location.href = 'dashboard.html';
            }

            // Alt + T for tasks
            if (e.altKey && e.key === 't') {
                e.preventDefault();
                window.location.href = 'tasks.html';
            }
        });
    }

    // Quick search functionality
    openQuickSearch() {
        // Create quick search modal if it doesn't exist
        let searchModal = document.getElementById('quick-search-modal');
        if (!searchModal) {
            searchModal = this.createQuickSearchModal();
            document.body.appendChild(searchModal);
        }
        
        searchModal.classList.remove('hidden');
        searchModal.querySelector('input').focus();
    }

    createQuickSearchModal() {
        const modal = document.createElement('div');
        modal.id = 'quick-search-modal';
        modal.className = 'modal';
        modal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Quick Search</h3>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <input type="text" placeholder="Search pages, tasks, or features..." class="search-input">
                    <div class="search-results"></div>
                </div>
            </div>
        `;

        // Setup search functionality
        const searchInput = modal.querySelector('.search-input');
        const searchResults = modal.querySelector('.search-results');
        const closeBtn = modal.querySelector('.modal-close');

        searchInput.addEventListener('input', (e) => {
            this.performSearch(e.target.value, searchResults);
        });

        closeBtn.addEventListener('click', () => {
            modal.classList.add('hidden');
        });

        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                modal.classList.add('hidden');
            }
        });

        return modal;
    }

    performSearch(query, resultsContainer) {
        if (!query.trim()) {
            resultsContainer.innerHTML = '';
            return;
        }

        const searchItems = [
            { title: 'Dashboard', url: 'dashboard.html', description: 'View your progress and overview' },
            { title: 'Tasks', url: 'tasks.html', description: 'Complete daily wellness tasks' },
            { title: 'Analytics', url: 'analytics.html', description: 'View detailed analytics and insights' },
            { title: 'Settings', url: 'settings.html', description: 'Customize your experience' },
            { title: 'AI Companion', url: 'bot.html', description: 'Chat with your AI wellness companion' },
            { title: 'Goals', url: 'goals.html', description: 'Set and track your wellness goals' },
            { title: 'Sleep Tracker', url: 'sleep-tracker.html', description: 'Monitor your sleep patterns' },
            { title: 'Nutrition Tracker', url: 'nutrition-tracker.html', description: 'Track your nutrition intake' },
            { title: 'VR Meditation', url: 'vr-meditation.html', description: 'Immersive meditation experience' },
            { title: 'Rewards', url: 'rewards.html', description: 'View your achievements and badges' }
        ];

        const filteredItems = searchItems.filter(item => 
            item.title.toLowerCase().includes(query.toLowerCase()) ||
            item.description.toLowerCase().includes(query.toLowerCase())
        );

        resultsContainer.innerHTML = filteredItems.map(item => `
            <div class="search-result-item" onclick="window.location.href='${item.url}'">
                <h4>${item.title}</h4>
                <p>${item.description}</p>
            </div>
        `).join('');
    }

    // Close all modals
    closeAllModals() {
        document.querySelectorAll('.modal:not(.hidden)').forEach(modal => {
            modal.classList.add('hidden');
        });
    }

    // Accessibility enhancements
    setupAccessibility() {
        // Add focus indicators
        this.addFocusIndicators();
        
        // Setup screen reader announcements
        this.setupScreenReaderAnnouncements();
        
        // Add skip links
        this.addSkipLinks();
    }

    addFocusIndicators() {
        const style = document.createElement('style');
        style.textContent = `
            *:focus {
                outline: 2px solid #667eea !important;
                outline-offset: 2px !important;
            }
            
            .focus-visible {
                outline: 2px solid #667eea !important;
                outline-offset: 2px !important;
            }
        `;
        document.head.appendChild(style);
    }

    setupScreenReaderAnnouncements() {
        // Create announcement region
        const announcer = document.createElement('div');
        announcer.id = 'screen-reader-announcer';
        announcer.setAttribute('aria-live', 'polite');
        announcer.setAttribute('aria-atomic', 'true');
        announcer.style.cssText = `
            position: absolute;
            left: -10000px;
            width: 1px;
            height: 1px;
            overflow: hidden;
        `;
        document.body.appendChild(announcer);
    }

    announce(message) {
        const announcer = document.getElementById('screen-reader-announcer');
        if (announcer) {
            announcer.textContent = message;
        }
    }

    addSkipLinks() {
        const skipLink = document.createElement('a');
        skipLink.href = '#main-content';
        skipLink.textContent = 'Skip to main content';
        skipLink.className = 'skip-link';
        skipLink.style.cssText = `
            position: absolute;
            top: -40px;
            left: 6px;
            background: #667eea;
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: 4px;
            z-index: 1000;
        `;
        
        skipLink.addEventListener('focus', () => {
            skipLink.style.top = '6px';
        });
        
        skipLink.addEventListener('blur', () => {
            skipLink.style.top = '-40px';
        });
        
        document.body.insertBefore(skipLink, document.body.firstChild);
    }

    // Performance optimizations
    setupPerformanceOptimizations() {
        // Lazy load images
        this.setupLazyLoading();
        
        // Debounce scroll events
        this.setupScrollOptimization();
        
        // Preload critical resources
        this.preloadCriticalResources();
    }

    setupLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        observer.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }

    setupScrollOptimization() {
        let ticking = false;
        
        function updateScrollPosition() {
            // Handle scroll-based animations or updates here
            ticking = false;
        }
        
        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateScrollPosition);
                ticking = true;
            }
        }
        
        window.addEventListener('scroll', requestTick);
    }

    preloadCriticalResources() {
        const criticalResources = [
            'css/styles.css',
            'js/storage.js',
            'js/auth.js'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    }

    // Theme management
    setTheme(theme) {
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        if (theme !== 'light') {
            document.body.classList.add(`theme-${theme}`);
        }
        localStorage.setItem('mindjourney_theme', theme);
    }

    getTheme() {
        return localStorage.getItem('mindjourney_theme') || 'light';
    }

    // Data export/import utilities
    exportData() {
        const data = {
            userData: getUserData(),
            settings: JSON.parse(localStorage.getItem('mindjourney_settings') || '{}'),
            theme: this.getTheme(),
            exportDate: new Date().toISOString()
        };
        
        return JSON.stringify(data, null, 2);
    }

    importData(jsonData) {
        try {
            const data = JSON.parse(jsonData);
            
            if (data.userData) {
                saveUserData(data.userData);
            }
            
            if (data.settings) {
                localStorage.setItem('mindjourney_settings', JSON.stringify(data.settings));
            }
            
            if (data.theme) {
                this.setTheme(data.theme);
            }
            
            return { success: true, message: 'Data imported successfully!' };
        } catch (error) {
            return { success: false, message: 'Invalid data format.' };
        }
    }

    // Utility functions
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
}

// Initialize enhanced features
window.EnhancedFeatures = new EnhancedFeatures();

// Add CSS for search modal
const searchModalCSS = `
.search-input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    margin-bottom: 20px;
}

.search-input:focus {
    outline: none;
    border-color: #667eea;
}

.search-result-item {
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #eee;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item h4 {
    margin: 0 0 4px 0;
    color: #333;
}

.search-result-item p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.skip-link:focus {
    top: 6px !important;
}
`;

const style = document.createElement('style');
style.textContent = searchModalCSS;
document.head.appendChild(style);
