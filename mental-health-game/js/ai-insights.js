// AI Insights Engine for MindJourney

class AIInsightsEngine {
    constructor() {
        this.userData = null;
        this.insightsCache = {};
    }

    initialize() {
        this.userData = getUserData();
        if (!this.userData) {
            window.location.href = 'index.html';
            return;
        }

        this.updateAIStatus();
        this.setupEventListeners();
    }

    updateAIStatus() {
        const statusIndicator = document.querySelector('.ai-status-indicator');
        const statusDot = statusIndicator.querySelector('.status-dot');
        const statusText = statusIndicator.querySelector('.status-text');

        if (openAI && openAI.isConfigured) {
            statusDot.classList.add('connected');
            statusText.textContent = 'AI Analysis Ready';
        } else {
            statusDot.classList.add('disconnected');
            statusText.textContent = 'AI Not Configured - Limited Analysis';
        }
    }

    setupEventListeners() {
        document.getElementById('analyze-emotions').addEventListener('click', () => {
            this.generateEmotionalAnalysis();
        });

        document.getElementById('analyze-patterns').addEventListener('click', () => {
            this.generateBehavioralAnalysis();
        });

        document.getElementById('generate-recommendations').addEventListener('click', () => {
            this.generateRecommendations();
        });

        document.getElementById('generate-predictions').addEventListener('click', () => {
            this.generatePredictions();
        });

        document.getElementById('generate-weekly-summary').addEventListener('click', () => {
            this.generateWeeklySummary();
        });

        document.getElementById('ask-ai').addEventListener('click', () => {
            this.handleAIQuestion();
        });

        document.getElementById('ai-question').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.handleAIQuestion();
            }
        });

        // Export buttons
        document.getElementById('export-insights-pdf').addEventListener('click', () => {
            this.exportInsightsPDF();
        });

        document.getElementById('export-insights-json').addEventListener('click', () => {
            this.exportInsightsJSON();
        });

        document.getElementById('export-insights-summary').addEventListener('click', () => {
            this.exportInsightsSummary();
        });
    }

    async generateEmotionalAnalysis() {
        const container = document.getElementById('emotional-analysis');
        this.showLoading(container, 'Analyzing your emotional patterns...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicEmotionalAnalysis(container);
            return;
        }

        try {
            const moodData = this.prepareMoodData();
            const journalData = this.prepareJournalData();

            const prompt = `Analyze this user's emotional patterns and provide insights:

Mood Data (last 30 entries):
${moodData}

Journal Themes:
${journalData}

User Context:
- Level ${this.userData.level}, ${this.userData.xp} XP
- ${this.userData.streak} day streak
- ${this.userData.totalDays} total active days

Provide analysis in this format:
1. EMOTIONAL PATTERNS: Key patterns you observe
2. EMOTIONAL STRENGTHS: What they're doing well
3. GROWTH AREAS: Areas for emotional development
4. TRIGGERS: Potential emotional triggers identified
5. RECOMMENDATIONS: 3 specific emotional wellness recommendations

Be encouraging, specific, and actionable.`;

            const analysis = await openAI.makeAPICall([
                { role: 'system', content: 'You are an emotional intelligence expert analyzing wellness data.' },
                { role: 'user', content: prompt }
            ]);

            this.displayEmotionalAnalysis(container, analysis);
            this.insightsCache.emotional = analysis;

        } catch (error) {
            console.error('Error generating emotional analysis:', error);
            this.showBasicEmotionalAnalysis(container);
        }
    }

    async generateBehavioralAnalysis() {
        const container = document.getElementById('behavioral-analysis');
        this.showLoading(container, 'Analyzing your behavioral patterns...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicBehavioralAnalysis(container);
            return;
        }

        try {
            const behaviorData = this.prepareBehaviorData();

            const prompt = `Analyze behavioral patterns for this wellness journey:

Activity Data:
${behaviorData}

Streak Information:
- Current streak: ${this.userData.streak} days
- Total active days: ${this.userData.totalDays}
- Consistency rate: ${((this.userData.streak / this.userData.totalDays) * 100).toFixed(1)}%

Provide analysis in this format:
1. CONSISTENCY PATTERNS: When and how they're most consistent
2. ACTIVITY PREFERENCES: Which activities they gravitate toward
3. BEHAVIORAL STRENGTHS: Positive patterns identified
4. OPTIMIZATION OPPORTUNITIES: How to improve consistency
5. PERSONALIZED SCHEDULE: Suggested optimal routine

Be specific and actionable.`;

            const analysis = await openAI.makeAPICall([
                { role: 'system', content: 'You are a behavioral analyst specializing in wellness habits.' },
                { role: 'user', content: prompt }
            ]);

            this.displayBehavioralAnalysis(container, analysis);
            this.insightsCache.behavioral = analysis;

        } catch (error) {
            console.error('Error generating behavioral analysis:', error);
            this.showBasicBehavioralAnalysis(container);
        }
    }

    async generateRecommendations() {
        const container = document.getElementById('recommendations-grid');
        this.showLoading(container, 'Generating personalized recommendations...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicRecommendations(container);
            return;
        }

        try {
            const comprehensiveData = this.prepareComprehensiveData();

            const prompt = `Generate 6 personalized wellness recommendations based on this data:

${comprehensiveData}

For each recommendation, provide:
- Title (action-oriented)
- Description (why and how)
- Priority (High/Medium/Low)
- Expected impact
- Implementation steps

Focus on:
1. Immediate improvements (next week)
2. Habit optimization
3. Emotional wellness
4. Long-term growth
5. Challenge areas
6. Strength building

Format as JSON array with objects containing: title, description, priority, impact, steps.`;

            const recommendations = await openAI.makeAPICall([
                { role: 'system', content: 'You are a wellness coach generating personalized recommendations. Return valid JSON only.' },
                { role: 'user', content: prompt }
            ]);

            this.displayRecommendations(container, recommendations);
            this.insightsCache.recommendations = recommendations;

        } catch (error) {
            console.error('Error generating recommendations:', error);
            this.showBasicRecommendations(container);
        }
    }

    async generatePredictions() {
        const container = document.getElementById('prediction-container');
        this.showLoading(container, 'Generating predictive insights...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicPredictions(container);
            return;
        }

        try {
            const trendData = this.prepareTrendData();

            const prompt = `Based on this user's patterns, make predictive insights:

${trendData}

Provide predictions for:
1. OPTIMAL ACTIVITY TIMES: When they're most likely to succeed
2. POTENTIAL CHALLENGES: What obstacles to watch for
3. MOOD FORECASTING: Likely emotional patterns
4. GOAL ACHIEVEMENT: Probability of reaching current goals
5. BREAKTHROUGH MOMENTS: When significant progress might occur

Include confidence levels (High/Medium/Low) for each prediction.
Be encouraging but realistic.`;

            const predictions = await openAI.makeAPICall([
                { role: 'system', content: 'You are a predictive wellness analyst. Be encouraging but realistic.' },
                { role: 'user', content: prompt }
            ]);

            this.displayPredictions(container, predictions);
            this.insightsCache.predictions = predictions;

        } catch (error) {
            console.error('Error generating predictions:', error);
            this.showBasicPredictions(container);
        }
    }

    async generateWeeklySummary() {
        const container = document.getElementById('weekly-summary');
        this.showLoading(container, 'Generating weekly summary...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicWeeklySummary(container);
            return;
        }

        try {
            const summary = await advancedAI.generateWeeklyProgress(this.userData);
            this.displayWeeklySummary(container, summary);
            this.insightsCache.weekly = summary;

        } catch (error) {
            console.error('Error generating weekly summary:', error);
            this.showBasicWeeklySummary(container);
        }
    }

    async handleAIQuestion() {
        const question = document.getElementById('ai-question').value.trim();
        const container = document.getElementById('ai-response-container');

        if (!question) {
            alert('Please enter a question about your wellness data.');
            return;
        }

        this.showLoading(container, 'AI is analyzing your question...');

        if (!openAI || !openAI.isConfigured) {
            this.showBasicAIResponse(container, question);
            return;
        }

        try {
            const contextData = this.prepareComprehensiveData();

            const prompt = `User question: "${question}"

User's wellness data context:
${contextData}

Answer their question based on their specific data. Be:
- Specific to their data
- Encouraging and supportive
- Actionable when possible
- Honest about limitations

If the question can't be answered from the data, explain what data would be needed.`;

            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are answering questions about the user\'s specific wellness data.' },
                { role: 'user', content: prompt }
            ]);

            this.displayAIResponse(container, response);
            document.getElementById('ai-question').value = '';

        } catch (error) {
            console.error('Error handling AI question:', error);
            this.showBasicAIResponse(container, question);
        }
    }

    // Data preparation methods
    prepareMoodData() {
        const moods = this.userData.moodHistory.slice(-30);
        return moods.map(entry => 
            `${entry.date}: ${entry.mood}/4 (${this.getMoodLabel(entry.mood)})`
        ).join('\n');
    }

    prepareJournalData() {
        const entries = this.userData.journalEntries.slice(-10);
        return entries.map(entry => 
            `${entry.date}: ${entry.entry.substring(0, 100)}...`
        ).join('\n');
    }

    prepareBehaviorData() {
        const stats = this.userData.stats;
        return `
Breathing Sessions: ${stats.totalBreathingSessions}
Journal Entries: ${stats.totalJournalEntries}
Mood Check-ins: ${stats.totalMoodEntries}
Meditation Minutes: ${stats.totalMeditationMinutes}
Current Level: ${this.userData.level}
Total XP: ${this.userData.xp}
        `.trim();
    }

    prepareComprehensiveData() {
        return `
User Profile:
- Name: ${this.userData.username}
- Level: ${this.userData.level} (${this.userData.xp} XP)
- Streak: ${this.userData.streak} days
- Total Active Days: ${this.userData.totalDays}

Activity Stats:
- Breathing: ${this.userData.stats.totalBreathingSessions} sessions
- Journaling: ${this.userData.stats.totalJournalEntries} entries
- Mood Tracking: ${this.userData.stats.totalMoodEntries} entries
- Meditation: ${this.userData.stats.totalMeditationMinutes} minutes

Recent Mood Pattern:
${this.prepareMoodData()}

Badges Earned: ${this.userData.badges.length}
Active Goals: ${this.userData.goals ? this.userData.goals.filter(g => !g.completed).length : 0}
        `.trim();
    }

    prepareTrendData() {
        const recentMoods = this.userData.moodHistory.slice(-14);
        const avgMood = recentMoods.length > 0 
            ? recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length 
            : 2.5;

        return `
Recent Mood Trend: ${avgMood.toFixed(1)}/4 average
Consistency Rate: ${((this.userData.streak / this.userData.totalDays) * 100).toFixed(1)}%
Activity Frequency: ${this.userData.totalDays} days active
Progress Velocity: Level ${this.userData.level} in ${this.userData.totalDays} days
        `.trim();
    }

    // Display methods
    showLoading(container, message) {
        container.innerHTML = `
            <div class="analysis-loading">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    }

    displayEmotionalAnalysis(container, analysis) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>🧠 Emotional Intelligence Analysis</h4>
                <div class="analysis-content">${this.formatAnalysisText(analysis)}</div>
            </div>
        `;
    }

    displayBehavioralAnalysis(container, analysis) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>🔄 Behavioral Pattern Analysis</h4>
                <div class="analysis-content">${this.formatAnalysisText(analysis)}</div>
            </div>
        `;
    }

    displayRecommendations(container, recommendations) {
        try {
            const recs = JSON.parse(recommendations);
            const html = recs.map(rec => `
                <div class="recommendation-card">
                    <h4>🎯 ${rec.title}</h4>
                    <p>${rec.description}</p>
                    <div class="recommendation-meta">
                        <span class="recommendation-priority priority-${rec.priority.toLowerCase()}">${rec.priority} Priority</span>
                        <small>Expected Impact: ${rec.impact}</small>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        } catch (error) {
            container.innerHTML = `
                <div class="insight-card">
                    <h4>🎯 Personalized Recommendations</h4>
                    <div class="analysis-content">${this.formatAnalysisText(recommendations)}</div>
                </div>
            `;
        }
    }

    displayPredictions(container, predictions) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>🔮 Predictive Wellness Insights</h4>
                <div class="analysis-content">${this.formatAnalysisText(predictions)}</div>
            </div>
        `;
    }

    displayWeeklySummary(container, summary) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>📅 Weekly Progress Summary</h4>
                <div class="analysis-content">${this.formatAnalysisText(summary)}</div>
            </div>
        `;
    }

    displayAIResponse(container, response) {
        container.innerHTML = `
            <div class="ai-response">
                <h4>🤖 AI Response</h4>
                <p>${response}</p>
            </div>
        `;
    }

    formatAnalysisText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/(\d+\.\s*[A-Z\s]+:)/g, '<strong>$1</strong>')
            .replace(/([A-Z\s]+:)/g, '<strong>$1</strong>');
    }

    getMoodLabel(moodValue) {
        const labels = ['Very Sad', 'Sad', 'Okay', 'Happy', 'Very Happy'];
        return labels[moodValue] || 'Unknown';
    }

    // Basic fallback methods
    showBasicEmotionalAnalysis(container) {
        const avgMood = this.userData.moodHistory.length > 0 
            ? this.userData.moodHistory.reduce((sum, entry) => sum + entry.mood, 0) / this.userData.moodHistory.length 
            : 2.5;

        container.innerHTML = `
            <div class="insight-card">
                <h4>🧠 Basic Emotional Analysis</h4>
                <p><strong>Average Mood:</strong> ${avgMood.toFixed(1)}/4 (${this.getMoodLabel(Math.round(avgMood))})</p>
                <p><strong>Mood Entries:</strong> ${this.userData.moodHistory.length} total</p>
                <p><strong>Insight:</strong> ${avgMood > 3 ? 'You maintain a positive emotional state!' : avgMood > 2 ? 'Your emotional balance is developing well.' : 'Focus on mood-boosting activities.'}</p>
                <p><em>Enable AI features for detailed emotional pattern analysis.</em></p>
            </div>
        `;
    }

    showBasicBehavioralAnalysis(container) {
        const consistency = ((this.userData.streak / this.userData.totalDays) * 100).toFixed(1);
        
        container.innerHTML = `
            <div class="insight-card">
                <h4>🔄 Basic Behavioral Analysis</h4>
                <p><strong>Consistency Rate:</strong> ${consistency}%</p>
                <p><strong>Most Active:</strong> ${this.getMostActiveTask()}</p>
                <p><strong>Growth Area:</strong> ${this.getLeastActiveTask()}</p>
                <p><em>Enable AI features for detailed behavioral pattern analysis.</em></p>
            </div>
        `;
    }

    showBasicRecommendations(container) {
        container.innerHTML = `
            <div class="recommendation-card">
                <h4>🎯 Basic Recommendations</h4>
                <p>Maintain your current streak and try to complete all daily tasks.</p>
                <span class="recommendation-priority priority-medium">Medium Priority</span>
            </div>
            <div class="recommendation-card">
                <h4>🎯 Focus on Consistency</h4>
                <p>Build stronger habits by completing tasks at the same time each day.</p>
                <span class="recommendation-priority priority-high">High Priority</span>
            </div>
        `;
    }

    showBasicPredictions(container) {
        container.innerHTML = `
            <div class="prediction-item">
                <h4>🔮 Basic Prediction</h4>
                <p>Based on your current ${this.userData.streak}-day streak, you're likely to continue making progress.</p>
                <div class="prediction-confidence">Confidence: Medium</div>
            </div>
        `;
    }

    showBasicWeeklySummary(container) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>📅 Basic Weekly Summary</h4>
                <p>You're at level ${this.userData.level} with ${this.userData.xp} XP and a ${this.userData.streak}-day streak. Keep up the great work!</p>
            </div>
        `;
    }

    showBasicAIResponse(container, question) {
        container.innerHTML = `
            <div class="ai-response">
                <h4>🤖 Basic Response</h4>
                <p>I'd love to analyze your data in detail, but AI features aren't configured. Please set up your OpenAI API key in Settings for personalized insights about "${question}".</p>
            </div>
        `;
    }

    getMostActiveTask() {
        const stats = this.userData.stats;
        const tasks = {
            'Breathing': stats.totalBreathingSessions,
            'Journaling': stats.totalJournalEntries,
            'Mood Tracking': stats.totalMoodEntries,
            'Meditation': stats.totalMeditationMinutes
        };
        
        return Object.keys(tasks).reduce((a, b) => tasks[a] > tasks[b] ? a : b);
    }

    getLeastActiveTask() {
        const stats = this.userData.stats;
        const tasks = {
            'Breathing': stats.totalBreathingSessions,
            'Journaling': stats.totalJournalEntries,
            'Mood Tracking': stats.totalMoodEntries,
            'Meditation': stats.totalMeditationMinutes
        };
        
        return Object.keys(tasks).reduce((a, b) => tasks[a] < tasks[b] ? a : b);
    }

    // Export methods
    exportInsightsPDF() {
        alert('PDF export feature coming soon! For now, you can copy the insights text.');
    }

    exportInsightsJSON() {
        const insights = {
            exportDate: new Date().toISOString(),
            userData: {
                level: this.userData.level,
                xp: this.userData.xp,
                streak: this.userData.streak
            },
            insights: this.insightsCache
        };
        
        const dataStr = JSON.stringify(insights, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `mindjourney-ai-insights-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        URL.revokeObjectURL(url);
    }

    exportInsightsSummary() {
        const summary = `MindJourney AI Insights Summary
Generated: ${new Date().toLocaleDateString()}

User: ${this.userData.username}
Level: ${this.userData.level}
Streak: ${this.userData.streak} days

=== AI INSIGHTS ===
${Object.entries(this.insightsCache).map(([key, value]) => 
    `${key.toUpperCase()}:\n${value}\n`
).join('\n')}

Generated by MindJourney AI Insights Engine`;

        const dataBlob = new Blob([summary], { type: 'text/plain' });
        const url = URL.createObjectURL(dataBlob);
        
        const link = document.createElement('a');
        link.href = url;
        link.download = `mindjourney-insights-summary-${new Date().toISOString().split('T')[0]}.txt`;
        link.click();
        
        URL.revokeObjectURL(url);
    }
}

// Initialize AI Insights Engine
const aiInsights = new AIInsightsEngine();

document.addEventListener('DOMContentLoaded', function() {
    aiInsights.initialize();
});
