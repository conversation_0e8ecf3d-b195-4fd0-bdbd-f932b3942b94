// Gamification Logic for MindJourney App

// Achievement system
class AchievementSystem {
    constructor() {
        this.achievements = [];
        this.unlockedAchievements = [];
    }

    // Check for streak-based achievements
    checkStreakAchievements(streak) {
        const achievements = [];
        
        if (streak >= 3 && !this.hasAchievement('consistent-soul')) {
            achievements.push('consistent-soul');
        }
        
        if (streak >= 7 && !this.hasAchievement('week-warrior')) {
            achievements.push('week-warrior');
        }
        
        if (streak >= 30 && !this.hasAchievement('month-master')) {
            achievements.push('month-master');
        }
        
        return achievements;
    }

    // Check for task completion achievements
    checkTaskAchievements(taskType, count) {
        const achievements = [];
        
        switch (taskType) {
            case 'breathing':
                if (count === 1 && !this.hasAchievement('calm-starter')) {
                    achievements.push('calm-starter');
                }
                if (count >= 20 && !this.hasAchievement('zen-master')) {
                    achievements.push('zen-master');
                }
                break;
                
            case 'journal':
                if (count >= 10 && !this.hasAchievement('mindful-writer')) {
                    achievements.push('mindful-writer');
                }
                break;
                
            case 'mood':
                if (count >= 7 && !this.hasAchievement('mood-master')) {
                    achievements.push('mood-master');
                }
                break;
                
            case 'meditation':
                if (count >= 60 && !this.hasAchievement('peaceful-heart')) {
                    achievements.push('peaceful-heart');
                }
                break;
        }
        
        return achievements;
    }

    hasAchievement(achievementId) {
        const userData = getUserData();
        return userData && userData.badges.includes(achievementId);
    }
}

// Level system
class LevelSystem {
    constructor() {
        this.maxLevel = 10;
        this.xpRequirements = {
            1: 0, 2: 50, 3: 120, 4: 200, 5: 300,
            6: 420, 7: 560, 8: 720, 9: 900, 10: 1100
        };
    }

    calculateLevel(xp) {
        for (let level = this.maxLevel; level >= 1; level--) {
            if (xp >= this.xpRequirements[level]) {
                return level;
            }
        }
        return 1;
    }

    getXpForNextLevel(currentLevel) {
        if (currentLevel >= this.maxLevel) {
            return null; // Max level reached
        }
        return this.xpRequirements[currentLevel + 1];
    }

    getProgressToNextLevel(currentXp, currentLevel) {
        if (currentLevel >= this.maxLevel) {
            return { isMaxLevel: true, progress: 100 };
        }

        const currentLevelXp = this.xpRequirements[currentLevel];
        const nextLevelXp = this.xpRequirements[currentLevel + 1];
        const progressXp = currentXp - currentLevelXp;
        const requiredXp = nextLevelXp - currentLevelXp;
        const progress = Math.round((progressXp / requiredXp) * 100);

        return {
            isMaxLevel: false,
            progress: Math.min(progress, 100),
            currentXp: progressXp,
            requiredXp,
            nextLevel: currentLevel + 1
        };
    }
}

// Reward system
class RewardSystem {
    constructor() {
        this.dailyRewards = {
            breathing: { xp: 10, message: "Great breathing session! You've earned 10 XP." },
            journal: { xp: 15, message: "Wonderful reflection! You've earned 15 XP." },
            mood: { xp: 5, message: "Thanks for checking in! You've earned 5 XP." },
            meditation: { xp: 20, message: "Peaceful meditation! You've earned 20 XP." },
            chatBot: { xp: 10, message: "Great conversation! You've earned 10 XP." }
        };
        
        this.bonusRewards = {
            allTasksOneDay: { xp: 25, message: "Amazing! All tasks completed today! Bonus 25 XP!" },
            firstTimeUser: { xp: 50, message: "Welcome to MindJourney! Here's 50 XP to get you started!" },
            levelUp: { xp: 0, message: "Congratulations! You've reached a new level!" }
        };
    }

    getTaskReward(taskType) {
        return this.dailyRewards[taskType] || { xp: 0, message: "Task completed!" };
    }

    getBonusReward(bonusType) {
        return this.bonusRewards[bonusType] || { xp: 0, message: "Bonus earned!" };
    }

    calculateDailyBonus(userData) {
        const today = getCurrentDate();
        const completedTasks = Object.values(userData.dailyTasks).filter(
            task => task.completed && task.date === today
        );
        
        if (completedTasks.length === 4) {
            return this.getBonusReward('allTasksOneDay');
        }
        
        return null;
    }
}

// Motivation system
class MotivationSystem {
    constructor() {
        this.encouragementMessages = {
            lowStreak: [
                "Every journey begins with a single step. You're on your way!",
                "Starting fresh is a sign of strength, not failure.",
                "Today is a new opportunity to care for your mental health."
            ],
            mediumStreak: [
                "You're building a wonderful habit! Keep it up!",
                "Your consistency is paying off. You should be proud!",
                "You're creating positive change in your life, one day at a time."
            ],
            highStreak: [
                "Incredible dedication! You're a mental wellness champion!",
                "Your commitment to self-care is truly inspiring!",
                "You've built an amazing routine. You're unstoppable!"
            ],
            levelUp: [
                "Level up! Your growth is remarkable!",
                "New level unlocked! You're becoming stronger every day!",
                "Amazing progress! You've reached new heights!"
            ],
            badgeEarned: [
                "Badge earned! Your hard work is paying off!",
                "Achievement unlocked! You're doing fantastic!",
                "New badge! Your dedication is inspiring!"
            ]
        };
        
        this.dailyTips = [
            "Remember to take deep breaths throughout the day.",
            "Small acts of self-care can make a big difference.",
            "It's okay to have difficult days. Be gentle with yourself.",
            "Progress isn't always linear, and that's perfectly normal.",
            "Celebrating small wins is just as important as big achievements.",
            "Your mental health journey is unique and valuable.",
            "Taking time for yourself isn't selfish, it's necessary.",
            "Every moment of mindfulness counts, no matter how brief."
        ];
    }

    getEncouragementMessage(type, streak = 0) {
        let messageArray;
        
        switch (type) {
            case 'streak':
                if (streak <= 2) messageArray = this.encouragementMessages.lowStreak;
                else if (streak <= 7) messageArray = this.encouragementMessages.mediumStreak;
                else messageArray = this.encouragementMessages.highStreak;
                break;
            case 'levelUp':
                messageArray = this.encouragementMessages.levelUp;
                break;
            case 'badgeEarned':
                messageArray = this.encouragementMessages.badgeEarned;
                break;
            default:
                messageArray = this.encouragementMessages.lowStreak;
        }
        
        return messageArray[Math.floor(Math.random() * messageArray.length)];
    }

    getDailyTip() {
        return this.dailyTips[Math.floor(Math.random() * this.dailyTips.length)];
    }

    getPersonalizedMessage(userData) {
        const messages = [];
        
        // Based on level
        if (userData.level >= 5) {
            messages.push(`You're already at level ${userData.level}! Your dedication is remarkable.`);
        }
        
        // Based on streak
        if (userData.streak >= 7) {
            messages.push(`Your ${userData.streak}-day streak shows incredible consistency!`);
        } else if (userData.streak >= 3) {
            messages.push(`You're building momentum with your ${userData.streak}-day streak!`);
        }
        
        // Based on badges
        if (userData.badges.length >= 5) {
            messages.push(`You've earned ${userData.badges.length} badges - you're a true wellness warrior!`);
        }
        
        // Based on activity
        if (userData.stats.totalBreathingSessions >= 10) {
            messages.push("Your breathing practice is really developing!");
        }
        
        if (userData.stats.totalJournalEntries >= 5) {
            messages.push("Your journaling habit is creating positive change!");
        }
        
        return messages.length > 0 
            ? messages[Math.floor(Math.random() * messages.length)]
            : "You're doing great on your wellness journey!";
    }
}

// Progress tracking
class ProgressTracker {
    constructor() {
        this.milestones = {
            firstWeek: { days: 7, message: "First week complete! You're building a great foundation." },
            firstMonth: { days: 30, message: "One month of wellness! Your commitment is inspiring." },
            hundredXP: { xp: 100, message: "100 XP earned! You're making real progress." },
            fiveHundredXP: { xp: 500, message: "500 XP! You're becoming a wellness expert." },
            tenTasks: { tasks: 10, message: "10 tasks completed! You're building healthy habits." },
            fiftyTasks: { tasks: 50, message: "50 tasks done! Your dedication is remarkable." }
        };
    }

    checkMilestones(userData) {
        const achievements = [];
        const totalTasks = Object.values(userData.stats).reduce((sum, count) => sum + count, 0);
        
        // Check day milestones
        if (userData.totalDays >= 7 && !userData.milestones?.firstWeek) {
            achievements.push('firstWeek');
        }
        
        if (userData.totalDays >= 30 && !userData.milestones?.firstMonth) {
            achievements.push('firstMonth');
        }
        
        // Check XP milestones
        if (userData.xp >= 100 && !userData.milestones?.hundredXP) {
            achievements.push('hundredXP');
        }
        
        if (userData.xp >= 500 && !userData.milestones?.fiveHundredXP) {
            achievements.push('fiveHundredXP');
        }
        
        // Check task milestones
        if (totalTasks >= 10 && !userData.milestones?.tenTasks) {
            achievements.push('tenTasks');
        }
        
        if (totalTasks >= 50 && !userData.milestones?.fiftyTasks) {
            achievements.push('fiftyTasks');
        }
        
        return achievements;
    }

    getMilestoneMessage(milestoneId) {
        return this.milestones[milestoneId]?.message || "Milestone achieved!";
    }
}

// Initialize gamification systems
const achievementSystem = new AchievementSystem();
const levelSystem = new LevelSystem();
const rewardSystem = new RewardSystem();
const motivationSystem = new MotivationSystem();
const progressTracker = new ProgressTracker();

// Export functions for use in other files
window.gamification = {
    achievementSystem,
    levelSystem,
    rewardSystem,
    motivationSystem,
    progressTracker
};
