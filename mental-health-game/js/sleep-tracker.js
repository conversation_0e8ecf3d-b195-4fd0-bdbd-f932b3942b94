// Sleep Tracker for MindJourney Enhanced

class SleepTracker {
    constructor() {
        this.userData = null;
        this.selectedQuality = null;
        this.sleepChart = null;
    }

    initialize() {
        this.userData = getUserData();
        if (!this.userData) {
            window.location.href = 'index.html';
            return;
        }

        // Initialize sleep data if it doesn't exist
        if (!this.userData.sleepData) {
            this.userData.sleepData = [];
            saveUserData(this.userData);
        }

        this.setupEventListeners();
        this.loadSleepData();
        this.updateStatistics();
        this.displaySleepHistory();
        this.drawSleepChart();
        
        // Set today's date as default
        document.getElementById('sleep-date').value = getCurrentDate();
    }

    setupEventListeners() {
        // Quality selector
        document.querySelectorAll('.quality-option').forEach(option => {
            option.addEventListener('click', () => {
                document.querySelectorAll('.quality-option').forEach(opt => opt.classList.remove('selected'));
                option.classList.add('selected');
                this.selectedQuality = parseInt(option.dataset.quality);
            });
        });

        // Log sleep button
        document.getElementById('log-sleep').addEventListener('click', () => {
            this.logSleepEntry();
        });

        // AI analysis button
        document.getElementById('analyze-sleep').addEventListener('click', () => {
            this.generateSleepAnalysis();
        });
    }

    logSleepEntry() {
        const bedtime = document.getElementById('bedtime').value;
        const wakeTime = document.getElementById('wake-time').value;
        const sleepDate = document.getElementById('sleep-date').value;
        const notes = document.getElementById('sleep-notes').value;

        if (!bedtime || !wakeTime || !sleepDate || this.selectedQuality === null) {
            alert('Please fill in all required fields and select a sleep quality rating.');
            return;
        }

        // Calculate sleep duration
        const duration = this.calculateSleepDuration(bedtime, wakeTime);
        
        // Get selected factors
        const factors = Array.from(document.querySelectorAll('input[name="factors"]:checked'))
            .map(checkbox => checkbox.value);

        const sleepEntry = {
            id: 'sleep_' + Date.now(),
            date: sleepDate,
            bedtime,
            wakeTime,
            duration,
            quality: this.selectedQuality,
            factors,
            notes,
            timestamp: new Date().toISOString()
        };

        // Add to sleep data
        this.userData.sleepData.push(sleepEntry);
        
        // Update stats
        if (!this.userData.stats.sleepEntries) {
            this.userData.stats.sleepEntries = 0;
        }
        this.userData.stats.sleepEntries++;

        // Award XP for logging sleep
        addXP(15);
        
        saveUserData(this.userData);
        
        // Update displays
        this.updateStatistics();
        this.displaySleepHistory();
        this.drawSleepChart();
        
        // Clear form
        this.clearForm();
        
        // Show success message
        this.showSuccessMessage('Sleep entry logged successfully! +15 XP earned!');
    }

    calculateSleepDuration(bedtime, wakeTime) {
        const bedDate = new Date(`2000-01-01 ${bedtime}`);
        let wakeDate = new Date(`2000-01-01 ${wakeTime}`);
        
        // If wake time is earlier than bedtime, assume it's the next day
        if (wakeDate <= bedDate) {
            wakeDate.setDate(wakeDate.getDate() + 1);
        }
        
        const durationMs = wakeDate - bedDate;
        const hours = Math.floor(durationMs / (1000 * 60 * 60));
        const minutes = Math.floor((durationMs % (1000 * 60 * 60)) / (1000 * 60));
        
        return { hours, minutes, totalMinutes: hours * 60 + minutes };
    }

    updateStatistics() {
        const sleepData = this.userData.sleepData;
        
        if (sleepData.length === 0) {
            document.getElementById('avg-duration').textContent = '0h 0m';
            document.getElementById('avg-quality').textContent = '0.0';
            document.getElementById('avg-bedtime').textContent = '--:--';
            document.getElementById('avg-waketime').textContent = '--:--';
            return;
        }

        // Calculate averages
        const avgDuration = sleepData.reduce((sum, entry) => sum + entry.duration.totalMinutes, 0) / sleepData.length;
        const avgQuality = sleepData.reduce((sum, entry) => sum + entry.quality, 0) / sleepData.length;
        
        // Calculate average bedtime and wake time
        const avgBedtime = this.calculateAverageTime(sleepData.map(entry => entry.bedtime));
        const avgWakeTime = this.calculateAverageTime(sleepData.map(entry => entry.wakeTime));

        // Update display
        const avgHours = Math.floor(avgDuration / 60);
        const avgMinutes = Math.floor(avgDuration % 60);
        
        document.getElementById('avg-duration').textContent = `${avgHours}h ${avgMinutes}m`;
        document.getElementById('avg-quality').textContent = avgQuality.toFixed(1);
        document.getElementById('avg-bedtime').textContent = avgBedtime;
        document.getElementById('avg-waketime').textContent = avgWakeTime;
    }

    calculateAverageTime(times) {
        if (times.length === 0) return '--:--';
        
        const totalMinutes = times.reduce((sum, time) => {
            const [hours, minutes] = time.split(':').map(Number);
            return sum + (hours * 60 + minutes);
        }, 0);
        
        const avgMinutes = Math.round(totalMinutes / times.length);
        const hours = Math.floor(avgMinutes / 60);
        const minutes = avgMinutes % 60;
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    }

    displaySleepHistory() {
        const historyContainer = document.getElementById('sleep-history');
        const sleepData = this.userData.sleepData.slice(-10).reverse(); // Last 10 entries, newest first

        if (sleepData.length === 0) {
            historyContainer.innerHTML = '<div class="no-entries"><p>No sleep entries yet. Start logging your sleep to see your history!</p></div>';
            return;
        }

        const historyHTML = sleepData.map(entry => `
            <div class="sleep-entry">
                <div class="sleep-entry-info">
                    <div class="sleep-entry-date">${this.formatDate(entry.date)}</div>
                    <div class="sleep-entry-details">
                        ${entry.bedtime} - ${entry.wakeTime} (${entry.duration.hours}h ${entry.duration.minutes}m)
                        ${entry.factors.length > 0 ? `• Factors: ${entry.factors.join(', ')}` : ''}
                    </div>
                    ${entry.notes ? `<div class="sleep-entry-notes">"${entry.notes}"</div>` : ''}
                </div>
                <div class="sleep-entry-quality">
                    ${this.getQualityEmoji(entry.quality)} ${entry.quality}/5
                </div>
            </div>
        `).join('');

        historyContainer.innerHTML = historyHTML;
    }

    drawSleepChart() {
        const canvas = document.getElementById('sleep-chart');
        const ctx = canvas.getContext('2d');
        const sleepData = this.userData.sleepData.slice(-14); // Last 14 days

        // Clear canvas
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        if (sleepData.length === 0) {
            ctx.fillStyle = '#666';
            ctx.font = '16px Arial';
            ctx.textAlign = 'center';
            ctx.fillText('No sleep data available', canvas.width / 2, canvas.height / 2);
            return;
        }

        const padding = 40;
        const chartWidth = canvas.width - (padding * 2);
        const chartHeight = canvas.height - (padding * 2);

        // Draw axes
        ctx.strokeStyle = '#ddd';
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(padding, padding);
        ctx.lineTo(padding, canvas.height - padding);
        ctx.lineTo(canvas.width - padding, canvas.height - padding);
        ctx.stroke();

        // Draw sleep duration line
        if (sleepData.length > 1) {
            ctx.strokeStyle = '#667eea';
            ctx.lineWidth = 3;
            ctx.beginPath();

            sleepData.forEach((entry, index) => {
                const x = padding + (index / (sleepData.length - 1)) * chartWidth;
                const y = canvas.height - padding - (entry.duration.totalMinutes / 600) * chartHeight; // Scale to 10 hours max

                if (index === 0) {
                    ctx.moveTo(x, y);
                } else {
                    ctx.lineTo(x, y);
                }
            });

            ctx.stroke();
        }

        // Draw quality points
        sleepData.forEach((entry, index) => {
            const x = padding + (index / Math.max(sleepData.length - 1, 1)) * chartWidth;
            const durationY = canvas.height - padding - (entry.duration.totalMinutes / 600) * chartHeight;
            
            // Duration point
            ctx.fillStyle = '#667eea';
            ctx.beginPath();
            ctx.arc(x, durationY, 4, 0, 2 * Math.PI);
            ctx.fill();

            // Quality indicator (color-coded)
            const qualityY = canvas.height - padding - (entry.quality / 5) * chartHeight;
            ctx.fillStyle = this.getQualityColor(entry.quality);
            ctx.beginPath();
            ctx.arc(x, qualityY, 6, 0, 2 * Math.PI);
            ctx.fill();
        });

        // Draw legend
        ctx.fillStyle = '#666';
        ctx.font = '12px Arial';
        ctx.textAlign = 'left';
        ctx.fillText('Duration (hours)', padding, 20);
        ctx.fillText('Quality (1-5)', padding + 150, 20);
    }

    async generateSleepAnalysis() {
        const analysisContainer = document.getElementById('sleep-ai-analysis');
        
        if (!isAIAvailable()) {
            analysisContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <p>AI analysis requires OpenAI configuration. Please set up your API key in Settings to get personalized sleep insights.</p>
                </div>
            `;
            return;
        }

        this.showLoading(analysisContainer, 'Analyzing your sleep patterns...');

        try {
            const sleepData = this.userData.sleepData.slice(-30); // Last 30 days
            const sleepSummary = this.prepareSleepSummary(sleepData);

            const prompt = `Analyze this user's sleep patterns and provide insights:

Sleep Data Summary:
${sleepSummary}

User Context:
- Name: ${this.userData.username}
- Wellness Level: ${this.userData.level}
- Current Streak: ${this.userData.streak} days

Provide analysis in this format:
1. SLEEP PATTERNS: Key patterns observed in their sleep
2. QUALITY TRENDS: How their sleep quality is trending
3. DURATION ANALYSIS: Assessment of sleep duration patterns
4. FACTOR CORRELATIONS: How different factors affect their sleep
5. RECOMMENDATIONS: 3 specific, actionable sleep improvement suggestions
6. OPTIMAL SCHEDULE: Suggested ideal bedtime and wake time

Be encouraging, specific, and focus on actionable insights.`;

            const analysis = await openAI.makeAPICall([
                { role: 'system', content: getPersonalityPrompt() + ' You are analyzing sleep patterns for wellness optimization.' },
                { role: 'user', content: prompt }
            ]);

            this.displaySleepAnalysis(analysisContainer, analysis);

        } catch (error) {
            console.error('Error generating sleep analysis:', error);
            analysisContainer.innerHTML = `
                <div class="analysis-placeholder">
                    <p>Unable to generate sleep analysis at this time. Please try again later.</p>
                </div>
            `;
        }
    }

    prepareSleepSummary(sleepData) {
        if (sleepData.length === 0) {
            return 'No sleep data available for analysis.';
        }

        const avgDuration = sleepData.reduce((sum, entry) => sum + entry.duration.totalMinutes, 0) / sleepData.length;
        const avgQuality = sleepData.reduce((sum, entry) => sum + entry.quality, 0) / sleepData.length;
        
        const factorCounts = {};
        sleepData.forEach(entry => {
            entry.factors.forEach(factor => {
                factorCounts[factor] = (factorCounts[factor] || 0) + 1;
            });
        });

        const commonFactors = Object.entries(factorCounts)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 3)
            .map(([factor, count]) => `${factor} (${count} times)`);

        return `
Total Entries: ${sleepData.length}
Average Duration: ${Math.floor(avgDuration / 60)}h ${Math.floor(avgDuration % 60)}m
Average Quality: ${avgQuality.toFixed(1)}/5
Most Common Factors: ${commonFactors.join(', ') || 'None'}
Date Range: ${sleepData[0].date} to ${sleepData[sleepData.length - 1].date}
        `.trim();
    }

    displaySleepAnalysis(container, analysis) {
        container.innerHTML = `
            <div class="insight-card">
                <h4>🧠 AI Sleep Analysis</h4>
                <div class="analysis-content">${this.formatAnalysisText(analysis)}</div>
            </div>
        `;
    }

    formatAnalysisText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/(\d+\.\s*[A-Z\s]+:)/g, '<strong>$1</strong>')
            .replace(/([A-Z\s]+:)/g, '<strong>$1</strong>');
    }

    showLoading(container, message) {
        container.innerHTML = `
            <div class="analysis-loading">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        `;
    }

    getQualityEmoji(quality) {
        const emojis = ['😴', '😐', '😊', '😄', '🤩'];
        return emojis[quality - 1] || '😐';
    }

    getQualityColor(quality) {
        const colors = ['#ff6b6b', '#ffa726', '#ffeb3b', '#66bb6a', '#4caf50'];
        return colors[quality - 1] || '#ccc';
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        });
    }

    clearForm() {
        document.getElementById('sleep-notes').value = '';
        document.querySelectorAll('.quality-option').forEach(opt => opt.classList.remove('selected'));
        document.querySelectorAll('input[name="factors"]').forEach(checkbox => checkbox.checked = false);
        this.selectedQuality = null;
    }

    showSuccessMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'success-message';
        messageDiv.textContent = message;
        messageDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #56ab2f;
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 10px;
            z-index: 1000;
            animation: slideInRight 0.5s ease-out;
        `;
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            messageDiv.style.animation = 'slideOutRight 0.5s ease-in forwards';
            setTimeout(() => {
                if (messageDiv.parentNode) {
                    document.body.removeChild(messageDiv);
                }
            }, 500);
        }, 3000);
    }

    loadSleepData() {
        // Any additional data loading logic can go here
        console.log('Sleep data loaded:', this.userData.sleepData.length, 'entries');
    }
}

// Initialize sleep tracker
const sleepTracker = new SleepTracker();

document.addEventListener('DOMContentLoaded', function() {
    sleepTracker.initialize();
});
