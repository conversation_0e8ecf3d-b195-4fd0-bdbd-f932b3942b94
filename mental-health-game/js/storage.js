// localStorage Management for MindJourney App

// Default user data structure
const defaultUserData = {
    username: '',
    avatar: '',
    level: 1,
    xp: 0,
    streak: 0,
    lastLoginDate: null,
    totalDays: 0,
    badges: [],
    dailyTasks: {
        breathing: { completed: false, date: null },
        journal: { completed: false, date: null, entry: '' },
        mood: { completed: false, date: null, value: null },
        meditation: { completed: false, date: null }
    },
    journalEntries: [],
    moodHistory: [],
    stats: {
        totalBreathingSessions: 0,
        totalJournalEntries: 0,
        totalMoodEntries: 0,
        totalMeditationMinutes: 0
    },
    settings: {
        notifications: true,
        soundEnabled: true,
        darkMode: false
    }
};

// XP requirements for each level
const levelRequirements = {
    1: 0, 2: 50, 3: 120, 4: 200, 5: 300,
    6: 420, 7: 560, 8: 720, 9: 900, 10: 1100
};

// Badge definitions
const badgeDefinitions = {
    'calm-starter': {
        name: '<PERSON><PERSON> Starter',
        description: 'Complete your first breathing exercise',
        icon: '🌬️',
        requirement: 'breathing_first'
    },
    'consistent-soul': {
        name: 'Consistent Soul',
        description: 'Complete tasks for 3 days in a row',
        icon: '🔥',
        requirement: 'streak_3'
    },
    'resilient-mind': {
        name: 'Resilient Mind',
        description: 'Complete all tasks in one day',
        icon: '💪',
        requirement: 'all_tasks_one_day'
    },
    'mood-master': {
        name: 'Mood Master',
        description: 'Track your mood for 7 days',
        icon: '📊',
        requirement: 'mood_7_days'
    },
    'inner-warrior': {
        name: 'Inner Warrior',
        description: 'Reach level 10',
        icon: '⚔️',
        requirement: 'level_10'
    },
    'mindful-writer': {
        name: 'Mindful Writer',
        description: 'Write 10 journal entries',
        icon: '✍️',
        requirement: 'journal_10'
    },
    'zen-master': {
        name: 'Zen Master',
        description: 'Complete 20 breathing exercises',
        icon: '🧘',
        requirement: 'breathing_20'
    },
    'peaceful-heart': {
        name: 'Peaceful Heart',
        description: 'Meditate for 60 minutes total',
        icon: '💙',
        requirement: 'meditation_60'
    }
};

// Initialize user data
function initializeUser(username, avatar) {
    const userData = { ...defaultUserData };
    userData.username = username;
    userData.avatar = avatar;
    userData.lastLoginDate = getCurrentDate();
    saveUserData(userData);
    return userData;
}

// Get user data from localStorage
function getUserData() {
    try {
        const data = localStorage.getItem('mindjourney_user');
        return data ? JSON.parse(data) : null;
    } catch (error) {
        console.error('Error loading user data:', error);
        return null;
    }
}

// Save user data to localStorage
function saveUserData(userData) {
    try {
        localStorage.setItem('mindjourney_user', JSON.stringify(userData));
        return true;
    } catch (error) {
        console.error('Error saving user data:', error);
        return false;
    }
}

// Clear all user data
function clearUserData() {
    try {
        localStorage.removeItem('mindjourney_user');
        return true;
    } catch (error) {
        console.error('Error clearing user data:', error);
        return false;
    }
}

// Get current date string (YYYY-MM-DD)
function getCurrentDate() {
    return new Date().toISOString().split('T')[0];
}

// Check if it's a new day
function isNewDay(lastDate) {
    return getCurrentDate() !== lastDate;
}

// Update daily login and streak
function updateDailyLogin() {
    const userData = getUserData();
    if (!userData) return null;

    const today = getCurrentDate();
    const lastLogin = userData.lastLoginDate;

    if (isNewDay(lastLogin)) {
        // Reset daily tasks for new day
        Object.keys(userData.dailyTasks).forEach(task => {
            userData.dailyTasks[task].completed = false;
            userData.dailyTasks[task].date = null;
        });

        // Update streak
        if (lastLogin) {
            const lastLoginDate = new Date(lastLogin);
            const todayDate = new Date(today);
            const daysDiff = Math.floor((todayDate - lastLoginDate) / (1000 * 60 * 60 * 24));

            if (daysDiff === 1) {
                userData.streak += 1;
            } else if (daysDiff > 1) {
                userData.streak = 1; // Reset streak but count today
            }
        } else {
            userData.streak = 1;
        }

        userData.lastLoginDate = today;
        userData.totalDays += 1;
        saveUserData(userData);
    }

    return userData;
}

// Add XP and check for level up
function addXP(amount) {
    const userData = getUserData();
    if (!userData) return null;

    userData.xp += amount;
    
    // Check for level up
    const newLevel = calculateLevel(userData.xp);
    if (newLevel > userData.level) {
        userData.level = newLevel;
        saveUserData(userData);
        return { levelUp: true, newLevel, xp: userData.xp };
    }

    saveUserData(userData);
    return { levelUp: false, level: userData.level, xp: userData.xp };
}

// Calculate level based on XP
function calculateLevel(xp) {
    for (let level = 10; level >= 1; level--) {
        if (xp >= levelRequirements[level]) {
            return level;
        }
    }
    return 1;
}

// Complete a daily task
function completeTask(taskType, data = {}) {
    const userData = getUserData();
    if (!userData) return null;

    const today = getCurrentDate();
    
    // Mark task as completed
    userData.dailyTasks[taskType].completed = true;
    userData.dailyTasks[taskType].date = today;

    // Store task-specific data
    if (taskType === 'journal' && data.entry) {
        userData.dailyTasks[taskType].entry = data.entry;
        userData.journalEntries.push({
            date: today,
            entry: data.entry
        });
        userData.stats.totalJournalEntries += 1;
    }

    if (taskType === 'mood' && data.value !== undefined) {
        userData.dailyTasks[taskType].value = data.value;
        userData.moodHistory.push({
            date: today,
            mood: data.value
        });
        userData.stats.totalMoodEntries += 1;
    }

    if (taskType === 'breathing') {
        userData.stats.totalBreathingSessions += 1;
    }

    if (taskType === 'meditation' && data.minutes) {
        userData.stats.totalMeditationMinutes += data.minutes;
    }

    saveUserData(userData);
    return userData;
}

// Check and award badges
function checkBadges() {
    const userData = getUserData();
    if (!userData) return [];

    const newBadges = [];

    // Check each badge requirement
    Object.keys(badgeDefinitions).forEach(badgeId => {
        if (!userData.badges.includes(badgeId)) {
            const badge = badgeDefinitions[badgeId];
            let earned = false;

            switch (badge.requirement) {
                case 'breathing_first':
                    earned = userData.stats.totalBreathingSessions >= 1;
                    break;
                case 'streak_3':
                    earned = userData.streak >= 3;
                    break;
                case 'all_tasks_one_day':
                    earned = Object.values(userData.dailyTasks).every(task => 
                        task.completed && task.date === getCurrentDate()
                    );
                    break;
                case 'mood_7_days':
                    earned = userData.stats.totalMoodEntries >= 7;
                    break;
                case 'level_10':
                    earned = userData.level >= 10;
                    break;
                case 'journal_10':
                    earned = userData.stats.totalJournalEntries >= 10;
                    break;
                case 'breathing_20':
                    earned = userData.stats.totalBreathingSessions >= 20;
                    break;
                case 'meditation_60':
                    earned = userData.stats.totalMeditationMinutes >= 60;
                    break;
            }

            if (earned) {
                userData.badges.push(badgeId);
                newBadges.push(badge);
            }
        }
    });

    if (newBadges.length > 0) {
        saveUserData(userData);
    }

    return newBadges;
}

// Get badge by ID
function getBadge(badgeId) {
    return badgeDefinitions[badgeId] || null;
}

// Get all earned badges
function getEarnedBadges() {
    const userData = getUserData();
    if (!userData) return [];

    return userData.badges.map(badgeId => ({
        id: badgeId,
        ...badgeDefinitions[badgeId]
    }));
}

// Get progress to next level
function getProgressToNextLevel() {
    const userData = getUserData();
    if (!userData) return null;

    const currentLevel = userData.level;
    const currentXP = userData.xp;
    
    if (currentLevel >= 10) {
        return { isMaxLevel: true, progress: 100 };
    }

    const currentLevelXP = levelRequirements[currentLevel];
    const nextLevelXP = levelRequirements[currentLevel + 1];
    const progressXP = currentXP - currentLevelXP;
    const requiredXP = nextLevelXP - currentLevelXP;
    const progress = Math.round((progressXP / requiredXP) * 100);

    return {
        isMaxLevel: false,
        progress: Math.min(progress, 100),
        currentXP: progressXP,
        requiredXP,
        nextLevel: currentLevel + 1
    };
}
