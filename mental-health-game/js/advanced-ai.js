// Advanced AI Features for MindJourney

class AdvancedAI {
    constructor() {
        this.conversationMemory = [];
        this.userPersonality = null;
        this.emotionalState = null;
        this.sessionContext = {};
    }

    // Enhanced conversation with memory and context
    async enhancedConversation(message, userData) {
        if (!openAI || !openAI.isConfigured) {
            return this.getFallbackResponse(message);
        }

        try {
            // Analyze user's emotional state from message
            const emotionalAnalysis = await this.analyzeEmotion(message);
            this.emotionalState = emotionalAnalysis;

            // Build comprehensive context
            const context = this.buildConversationContext(userData, message);
            
            // Generate response with advanced prompting
            const response = await this.generateContextualResponse(message, context);
            
            // Store conversation in memory
            this.updateConversationMemory(message, response, emotionalAnalysis);
            
            return response;
        } catch (error) {
            console.error('Enhanced conversation error:', error);
            return this.getFallbackResponse(message);
        }
    }

    // Analyze emotional state from user message
    async analyzeEmotion(message) {
        const prompt = `Analyze the emotional state in this message: "${message}"

Return a JSON object with:
{
  "primary_emotion": "happy/sad/anxious/angry/neutral/excited/frustrated",
  "intensity": 1-5,
  "needs_support": true/false,
  "suggested_response_tone": "encouraging/empathetic/celebratory/calming"
}

Only return the JSON, nothing else.`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are an emotion analysis expert. Return only valid JSON.' },
                { role: 'user', content: prompt }
            ]);
            
            return JSON.parse(response);
        } catch (error) {
            return {
                primary_emotion: 'neutral',
                intensity: 3,
                needs_support: false,
                suggested_response_tone: 'encouraging'
            };
        }
    }

    // Build comprehensive conversation context
    buildConversationContext(userData, currentMessage) {
        const recentMoods = userData.moodHistory.slice(-7);
        const avgMood = recentMoods.length > 0 
            ? recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length 
            : 2.5;

        const context = {
            user: {
                name: userData.username,
                level: userData.level,
                xp: userData.xp,
                streak: userData.streak,
                totalDays: userData.totalDays,
                averageMood: avgMood.toFixed(1),
                recentActivity: this.getRecentActivity(userData),
                goals: userData.goals ? userData.goals.filter(g => !g.completed).length : 0
            },
            conversation: {
                previousMessages: this.conversationMemory.slice(-3),
                currentEmotion: this.emotionalState,
                sessionLength: this.conversationMemory.length
            },
            recommendations: this.generatePersonalizedRecommendations(userData)
        };

        return context;
    }

    // Generate contextual response with advanced prompting
    async generateContextualResponse(message, context) {
        const systemPrompt = `You are an advanced AI mental health companion with deep emotional intelligence. Your responses should be:

1. PERSONALIZED: Use the user's name (${context.user.name}) and reference their specific progress
2. EMOTIONALLY INTELLIGENT: Respond to their emotional state with appropriate tone
3. CONTEXTUALLY AWARE: Reference their recent activities, mood patterns, and goals
4. ACTIONABLE: Provide specific, helpful suggestions when appropriate
5. ENCOURAGING: Always maintain hope and positivity while validating feelings

User Context:
- Level ${context.user.level}, ${context.user.xp} XP, ${context.user.streak}-day streak
- Average mood: ${context.user.averageMood}/4 (recent week)
- Active goals: ${context.user.goals}
- Recent activity: ${context.user.recentActivity}

Conversation Context:
- Current emotion: ${context.conversation.currentEmotion?.primary_emotion || 'unknown'}
- Suggested tone: ${context.conversation.currentEmotion?.suggested_response_tone || 'encouraging'}
- Session length: ${context.conversation.sessionLength} messages

Respond in 2-4 sentences. Be warm, personal, and helpful.`;

        const userPrompt = `Current message: "${message}"

Based on all the context provided, respond as their caring AI companion.`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: systemPrompt },
                { role: 'user', content: userPrompt }
            ]);
            
            return response;
        } catch (error) {
            throw error;
        }
    }

    // Generate personalized daily check-in
    async generateDailyCheckIn(userData) {
        if (!openAI || !openAI.isConfigured) {
            return "How are you feeling today? I'm here to support you on your wellness journey.";
        }

        const context = this.buildConversationContext(userData, "daily check-in");
        
        const prompt = `Generate a personalized daily check-in message for ${userData.username}.

Context:
- Level ${userData.level}, ${userData.streak} day streak
- Recent mood average: ${context.user.averageMood}/4
- Recent activity: ${context.user.recentActivity}

Create a warm, personalized check-in that:
1. Acknowledges their progress
2. Asks about their current state
3. Offers specific support based on their patterns
4. Mentions a relevant wellness tip

Keep it conversational and caring (2-3 sentences).`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are a caring AI wellness companion doing a daily check-in.' },
                { role: 'user', content: prompt }
            ]);
            
            return response;
        } catch (error) {
            return "How are you feeling today? I'm here to support you on your wellness journey.";
        }
    }

    // Generate personalized meditation guidance
    async generateMeditationGuidance(userData, duration = 120) {
        if (!openAI || !openAI.isConfigured) {
            return "Find a comfortable position and focus on your breath. Let thoughts pass by like clouds in the sky.";
        }

        const recentMoods = userData.moodHistory.slice(-3);
        const currentMoodTrend = recentMoods.length > 0 ? 
            recentMoods[recentMoods.length - 1].mood : 2;

        const prompt = `Create personalized meditation guidance for a ${duration/60}-minute session.

User context:
- Name: ${userData.username}
- Recent mood trend: ${currentMoodTrend}/4
- Meditation experience: ${userData.stats.totalMeditationMinutes} total minutes
- Current stress level: ${currentMoodTrend < 2 ? 'high' : currentMoodTrend > 3 ? 'low' : 'moderate'}

Provide:
1. A personalized opening (acknowledge their state)
2. Specific breathing technique recommendation
3. Focus point suggestion based on their needs
4. Gentle closing affirmation

Keep it calming and personal (3-4 sentences).`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are a meditation guide creating personalized sessions.' },
                { role: 'user', content: prompt }
            ]);
            
            return response;
        } catch (error) {
            return "Find a comfortable position and focus on your breath. Let thoughts pass by like clouds in the sky.";
        }
    }

    // Generate crisis support response
    async generateCrisisSupport(message, userData) {
        if (!openAI || !openAI.isConfigured) {
            return this.getBasicCrisisResponse();
        }

        // Detect crisis keywords
        const crisisKeywords = ['suicide', 'kill myself', 'end it all', 'can\'t go on', 'hopeless', 'worthless'];
        const isCrisis = crisisKeywords.some(keyword => 
            message.toLowerCase().includes(keyword)
        );

        if (isCrisis) {
            return this.getBasicCrisisResponse();
        }

        const prompt = `The user seems to be in distress. Message: "${message}"

Provide immediate emotional support that:
1. Validates their feelings
2. Offers hope and perspective
3. Suggests immediate coping strategies
4. Reminds them of their strength and progress
5. Encourages professional help if needed

Be empathetic, immediate, and supportive (3-4 sentences).`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are providing crisis emotional support. Be immediate, caring, and helpful.' },
                { role: 'user', content: prompt }
            ]);
            
            return response + "\n\nIf you're in crisis, please reach out to a mental health professional or crisis hotline immediately.";
        } catch (error) {
            return this.getBasicCrisisResponse();
        }
    }

    // Generate weekly progress summary
    async generateWeeklyProgress(userData) {
        if (!openAI || !openAI.isConfigured) {
            return "You've made great progress this week! Keep up the excellent work on your wellness journey.";
        }

        const weeklyStats = this.calculateWeeklyStats(userData);
        
        const prompt = `Create a personalized weekly progress summary for ${userData.username}.

This week's stats:
- Tasks completed: ${weeklyStats.tasksCompleted}
- Mood entries: ${weeklyStats.moodEntries}
- Average mood: ${weeklyStats.averageMood}/4
- Streak maintained: ${userData.streak >= 7 ? 'Yes' : 'No'}
- XP gained: ${weeklyStats.xpGained}
- Goals progress: ${weeklyStats.goalsProgress}

Create an encouraging summary that:
1. Celebrates specific achievements
2. Acknowledges challenges if any
3. Provides motivation for next week
4. Suggests one specific improvement

Keep it personal and motivating (3-4 sentences).`;

        try {
            const response = await openAI.makeAPICall([
                { role: 'system', content: 'You are creating a weekly wellness progress summary.' },
                { role: 'user', content: prompt }
            ]);
            
            return response;
        } catch (error) {
            return "You've made great progress this week! Keep up the excellent work on your wellness journey.";
        }
    }

    // Helper methods
    getRecentActivity(userData) {
        const activities = [];
        const today = getCurrentDate();
        
        Object.keys(userData.dailyTasks).forEach(task => {
            if (userData.dailyTasks[task].completed && userData.dailyTasks[task].date === today) {
                activities.push(task);
            }
        });
        
        return activities.length > 0 ? activities.join(', ') : 'No activities today yet';
    }

    generatePersonalizedRecommendations(userData) {
        const recommendations = [];
        
        if (userData.streak === 0) {
            recommendations.push('Start a new streak today');
        }
        
        if (userData.stats.totalMeditationMinutes < 30) {
            recommendations.push('Try more meditation');
        }
        
        if (userData.moodHistory.length > 0) {
            const recentMood = userData.moodHistory[userData.moodHistory.length - 1].mood;
            if (recentMood < 2) {
                recommendations.push('Focus on mood-boosting activities');
            }
        }
        
        return recommendations;
    }

    updateConversationMemory(userMessage, aiResponse, emotion) {
        this.conversationMemory.push({
            timestamp: new Date().toISOString(),
            userMessage,
            aiResponse,
            emotion,
            context: 'conversation'
        });
        
        // Keep only last 10 conversations
        if (this.conversationMemory.length > 10) {
            this.conversationMemory = this.conversationMemory.slice(-10);
        }
    }

    calculateWeeklyStats(userData) {
        // This would calculate actual weekly stats
        // For now, return sample data
        return {
            tasksCompleted: userData.streak * 4, // Approximate
            moodEntries: Math.min(userData.stats.totalMoodEntries, 7),
            averageMood: 3.2,
            xpGained: 150,
            goalsProgress: '2 of 3 goals on track'
        };
    }

    getBasicCrisisResponse() {
        return `I'm concerned about you and want you to know that you're not alone. Your feelings are valid, but please remember that difficult times are temporary. 

🆘 If you're in immediate danger, please contact:
• Emergency Services: 911
• Crisis Text Line: Text HOME to 741741
• National Suicide Prevention Lifeline: 988

You matter, and there are people who want to help. Please reach out to a mental health professional, trusted friend, or family member.`;
    }

    getFallbackResponse(message) {
        const responses = [
            "I hear you, and I'm here to support you through this.",
            "Thank you for sharing that with me. How are you feeling about it?",
            "That sounds important to you. Would you like to explore it further?",
            "I appreciate your openness. What would be most helpful right now?",
            "Your feelings are valid. How can I best support you today?"
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
}

// Create global instance
const advancedAI = new AdvancedAI();

// Export for use in other files
window.advancedAI = advancedAI;
