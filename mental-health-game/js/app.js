// Main Application Logic for MindJourney

// XP values for different tasks
const XP_VALUES = {
    breathing: 10,
    journal: 15,
    mood: 5,
    meditation: 20,
    vrMeditation: 30, // VR meditation gives more XP
    chatBot: 10
};

// Motivational quotes for the AI bot
const motivationalQuotes = [
    "Every small step you take today is progress toward a better tomorrow.",
    "Your mental health journey is unique and valuable. Be patient with yourself.",
    "Breathing deeply is a simple way to reset your mind and find calm.",
    "Writing down your thoughts can help you understand yourself better.",
    "It's okay to have difficult days. What matters is that you're here, trying.",
    "Mindfulness is not about emptying your mind, but about being present.",
    "You are stronger than you think and more resilient than you know.",
    "Taking care of your mental health is not selfish, it's necessary.",
    "Progress, not perfection, is the goal of your wellness journey.",
    "Each moment of self-care is an investment in your future happiness."
];

// Journal prompts
const journalPrompts = [
    "What made you smile today?",
    "Describe three things you're grateful for right now.",
    "What challenge did you overcome recently?",
    "How are you feeling in this moment, and why?",
    "What would you tell a friend who was having a day like yours?",
    "What's one thing you learned about yourself this week?",
    "Describe a moment when you felt truly peaceful.",
    "What are you looking forward to?",
    "How did you show kindness to yourself or others today?",
    "What would make tomorrow a little bit better?"
];

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Update daily login and streak
    updateDailyLogin();
    
    // Check for new badges
    const newBadges = checkBadges();
    if (newBadges.length > 0) {
        showBadgeNotification(newBadges);
    }
});

// Show confetti animation
function showConfetti() {
    const container = document.createElement('div');
    container.className = 'confetti-container';
    document.body.appendChild(container);

    // Create confetti pieces
    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.className = 'confetti-piece';
        confetti.style.left = Math.random() * 100 + '%';
        confetti.style.animationDelay = Math.random() * 3 + 's';
        confetti.style.animationDuration = (Math.random() * 2 + 2) + 's';
        container.appendChild(confetti);
    }

    // Remove confetti after animation
    setTimeout(() => {
        document.body.removeChild(container);
    }, 5000);
}

// Show badge notification
function showBadgeNotification(badges) {
    badges.forEach((badge, index) => {
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.className = 'badge-notification';
            notification.innerHTML = `
                <div class="badge-unlock">
                    <div class="badge-icon">${badge.icon}</div>
                    <div class="badge-info">
                        <h3>Badge Unlocked!</h3>
                        <p><strong>${badge.name}</strong></p>
                        <p>${badge.description}</p>
                    </div>
                </div>
            `;
            
            // Add styles for notification
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: white;
                border-radius: 15px;
                padding: 20px;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                z-index: 1000;
                max-width: 300px;
                border: 3px solid #667eea;
            `;
            
            document.body.appendChild(notification);
            
            // Remove notification after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    document.body.removeChild(notification);
                }
            }, 5000);
            
            // Show confetti for badge unlock
            showConfetti();
        }, index * 1000);
    });
}

// Complete task and handle rewards
function completeTaskWithRewards(taskType, data = {}) {
    // Complete the task
    const userData = completeTask(taskType, data);
    if (!userData) return;

    // Add XP
    const xpResult = addXP(XP_VALUES[taskType]);
    
    // Check for new badges
    const newBadges = checkBadges();
    
    // Show success feedback
    showTaskCompletionFeedback(taskType, xpResult, newBadges);
    
    return { userData, xpResult, newBadges };
}

// Show task completion feedback
function showTaskCompletionFeedback(taskType, xpResult, newBadges) {
    // Show XP gain
    showXPGain(XP_VALUES[taskType]);
    
    // Show level up if applicable
    if (xpResult.levelUp) {
        showLevelUpNotification(xpResult.newLevel);
    }
    
    // Show new badges
    if (newBadges.length > 0) {
        showBadgeNotification(newBadges);
    }
    
    // Show confetti for task completion
    showConfetti();
}

// Show XP gain animation
function showXPGain(xp) {
    const xpElement = document.createElement('div');
    xpElement.textContent = `+${xp} XP`;
    xpElement.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #667eea, #764ba2);
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-weight: bold;
        font-size: 1.2rem;
        z-index: 1000;
        animation: slideInUp 0.5s ease-out, fadeOut 0.5s ease-in 2s forwards;
    `;
    
    document.body.appendChild(xpElement);
    
    setTimeout(() => {
        if (xpElement.parentNode) {
            document.body.removeChild(xpElement);
        }
    }, 2500);
}

// Show level up notification
function showLevelUpNotification(newLevel) {
    const notification = document.createElement('div');
    notification.innerHTML = `
        <div class="level-up-notification">
            <h2>🎉 Level Up! 🎉</h2>
            <p>You've reached Level ${newLevel}!</p>
        </div>
    `;
    
    notification.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: linear-gradient(45deg, #FFD700, #FFA500);
        color: white;
        padding: 30px;
        border-radius: 20px;
        text-align: center;
        z-index: 1001;
        box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        animation: bounce 0.6s ease-in-out;
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            document.body.removeChild(notification);
        }
    }, 3000);
}

// Get random motivational quote
function getRandomQuote() {
    return motivationalQuotes[Math.floor(Math.random() * motivationalQuotes.length)];
}

// Get random journal prompt
function getRandomJournalPrompt() {
    return journalPrompts[Math.floor(Math.random() * journalPrompts.length)];
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// Get mood emoji
function getMoodEmoji(moodValue) {
    const moods = ['😞', '😐', '🙂', '😃', '🤩'];
    return moods[moodValue] || '😐';
}

// Get mood label
function getMoodLabel(moodValue) {
    const labels = ['Very Sad', 'Sad', 'Okay', 'Happy', 'Very Happy'];
    return labels[moodValue] || 'Neutral';
}

// Update progress bar
function updateProgressBar(elementId, progress) {
    const progressBar = document.getElementById(elementId);
    if (progressBar) {
        progressBar.style.width = progress + '%';
        progressBar.classList.add('progress-fill');
    }
}

// Add glow effect to element
function addGlowEffect(element) {
    element.classList.add('glow');
    setTimeout(() => {
        element.classList.remove('glow');
    }, 2000);
}

// Add pulse effect to element
function addPulseEffect(element) {
    element.classList.add('pulse');
    setTimeout(() => {
        element.classList.remove('pulse');
    }, 2000);
}

// Breathing exercise controller
class BreathingExercise {
    constructor(container) {
        this.container = container;
        this.isRunning = false;
        this.currentPhase = 'ready';
        this.cycle = 0;
        this.totalCycles = 5;
    }

    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.cycle = 0;
        this.runCycle();
    }

    async runCycle() {
        while (this.cycle < this.totalCycles && this.isRunning) {
            await this.inhale();
            await this.hold();
            await this.exhale();
            this.cycle++;
        }
        
        if (this.isRunning) {
            this.complete();
        }
    }

    async inhale() {
        this.currentPhase = 'inhale';
        this.updateDisplay('Breathe In', 'inhale');
        await this.wait(4000);
    }

    async hold() {
        this.currentPhase = 'hold';
        this.updateDisplay('Hold', 'hold');
        await this.wait(4000);
    }

    async exhale() {
        this.currentPhase = 'exhale';
        this.updateDisplay('Breathe Out', 'exhale');
        await this.wait(6000);
    }

    updateDisplay(text, phase) {
        const circle = this.container.querySelector('.breathing-circle');
        const instruction = this.container.querySelector('.breathing-instruction');
        
        if (circle) {
            circle.className = `breathing-circle ${phase}`;
            circle.textContent = text;
        }
        
        if (instruction) {
            instruction.textContent = text;
        }
    }

    wait(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    complete() {
        this.isRunning = false;
        this.updateDisplay('Complete!', '');
        
        // Complete the task and show rewards
        completeTaskWithRewards('breathing');
        
        // Update UI to show completion
        const completeBtn = this.container.querySelector('.task-complete-btn');
        if (completeBtn) {
            completeBtn.textContent = 'Completed!';
            completeBtn.classList.add('completed');
            completeBtn.disabled = true;
        }
    }

    stop() {
        this.isRunning = false;
    }
}

// Meditation timer controller
class MeditationTimer {
    constructor(container, duration = 120) { // 2 minutes default
        this.container = container;
        this.duration = duration;
        this.timeLeft = duration;
        this.isRunning = false;
        this.interval = null;
    }

    start() {
        if (this.isRunning) return;
        
        this.isRunning = true;
        this.interval = setInterval(() => {
            this.timeLeft--;
            this.updateDisplay();
            
            if (this.timeLeft <= 0) {
                this.complete();
            }
        }, 1000);
        
        // Add meditation background
        document.body.classList.add('meditation-bg');
    }

    updateDisplay() {
        const minutes = Math.floor(this.timeLeft / 60);
        const seconds = this.timeLeft % 60;
        const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        
        const timerDisplay = this.container.querySelector('.timer-display');
        if (timerDisplay) {
            timerDisplay.textContent = timeString;
        }
    }

    complete() {
        this.isRunning = false;
        clearInterval(this.interval);
        
        // Remove meditation background and add sunrise effect
        document.body.classList.remove('meditation-bg');
        document.body.classList.add('meditation-bg', 'sunrise');
        
        setTimeout(() => {
            document.body.classList.remove('meditation-bg', 'sunrise');
        }, 3000);
        
        // Complete the task
        const minutes = Math.ceil(this.duration / 60);
        completeTaskWithRewards('meditation', { minutes });
        
        // Update UI
        const completeBtn = this.container.querySelector('.task-complete-btn');
        if (completeBtn) {
            completeBtn.textContent = 'Completed!';
            completeBtn.classList.add('completed');
            completeBtn.disabled = true;
        }
    }

    stop() {
        this.isRunning = false;
        if (this.interval) {
            clearInterval(this.interval);
        }
        document.body.classList.remove('meditation-bg', 'sunrise');
    }
}
