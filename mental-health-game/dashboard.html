<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="dashboard-page">
    <div class="container">
        <header class="dashboard-header">
            <div class="user-info">
                <div id="user-avatar" class="avatar-circle"></div>
                <div class="user-details">
                    <h2>Welcome back, <span id="username"></span>!</h2>
                    <p class="user-level">Level <span id="level">1</span></p>
                </div>
            </div>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link active">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="dashboard-content">
            <!-- Progress Overview -->
            <section class="progress-overview">
                <div class="dashboard-card">
                    <h3>Your Progress</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">XP</span>
                            <span id="xp-value" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Streak</span>
                            <span id="streak-value" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Days</span>
                            <span id="total-days" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Badges</span>
                            <span id="badge-count" class="stat-value">0</span>
                        </div>
                    </div>
                    
                    <div class="level-progress">
                        <h4>Progress to Next Level</h4>
                        <div class="progress-container">
                            <div id="level-progress-bar" class="progress-bar" style="width: 0%">
                                <span class="progress-text">0%</span>
                            </div>
                        </div>
                        <p id="level-progress-text">0 / 50 XP to Level 2</p>
                    </div>
                </div>
            </section>

            <!-- Daily Tasks Overview -->
            <section class="daily-tasks-overview">
                <div class="dashboard-card">
                    <h3>Today's Wellness Journey</h3>
                    <p class="date-display" id="current-date"></p>
                    
                    <div class="task-grid">
                        <div class="task-preview" id="breathing-preview">
                            <div class="task-icon">🌬️</div>
                            <div class="task-info">
                                <h4>Breathing Exercise</h4>
                                <p>5-minute calming session</p>
                                <span class="task-xp">+10 XP</span>
                            </div>
                            <div class="task-status" id="breathing-status">
                                <span class="status-indicator pending">Pending</span>
                            </div>
                        </div>

                        <div class="task-preview" id="journal-preview">
                            <div class="task-icon">📝</div>
                            <div class="task-info">
                                <h4>Daily Journal</h4>
                                <p>Reflect on your thoughts</p>
                                <span class="task-xp">+15 XP</span>
                            </div>
                            <div class="task-status" id="journal-status">
                                <span class="status-indicator pending">Pending</span>
                            </div>
                        </div>

                        <div class="task-preview" id="mood-preview">
                            <div class="task-icon">📊</div>
                            <div class="task-info">
                                <h4>Mood Check</h4>
                                <p>Track how you're feeling</p>
                                <span class="task-xp">+5 XP</span>
                            </div>
                            <div class="task-status" id="mood-status">
                                <span class="status-indicator pending">Pending</span>
                            </div>
                        </div>

                        <div class="task-preview" id="meditation-preview">
                            <div class="task-icon">🧘</div>
                            <div class="task-info">
                                <h4>Meditation</h4>
                                <p>2-minute mindfulness</p>
                                <span class="task-xp">+20 XP</span>
                            </div>
                            <div class="task-status" id="meditation-status">
                                <span class="status-indicator pending">Pending</span>
                            </div>
                        </div>
                    </div>

                    <div class="daily-progress">
                        <h4>Daily Completion</h4>
                        <div class="progress-container">
                            <div id="daily-progress-bar" class="progress-bar" style="width: 0%">
                                <span class="progress-text">0%</span>
                            </div>
                        </div>
                        <p id="daily-progress-text">0 of 4 tasks completed</p>
                    </div>

                    <a href="tasks.html" class="primary-btn">Start Today's Tasks</a>
                </div>
            </section>

            <!-- Recent Achievements -->
            <section class="recent-achievements">
                <div class="dashboard-card">
                    <h3>Recent Achievements</h3>
                    <div id="recent-badges" class="badge-showcase">
                        <p class="no-badges">Complete tasks to earn your first badge!</p>
                    </div>
                    <a href="rewards.html" class="secondary-btn">View All Rewards</a>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="dashboard-card">
                    <h3>Quick Actions</h3>
                    <div class="action-buttons">
                        <a href="bot.html" class="action-btn">
                            <span class="action-icon">🤖</span>
                            <span>Chat with AI Companion</span>
                        </a>
                        <button id="mood-quick-btn" class="action-btn">
                            <span class="action-icon">😊</span>
                            <span>Quick Mood Check</span>
                        </button>
                        <button id="breathing-quick-btn" class="action-btn">
                            <span class="action-icon">🌬️</span>
                            <span>Quick Breathing</span>
                        </button>
                        <a href="vr-meditation.html" class="action-btn">
                            <span class="action-icon">🥽</span>
                            <span>VR Meditation</span>
                        </a>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <!-- Quick Mood Modal -->
    <div id="quick-mood-modal" class="modal hidden">
        <div class="modal-content">
            <h3>How are you feeling right now?</h3>
            <div class="mood-selector">
                <div class="mood-option" data-mood="0">
                    <div class="mood-emoji">😞</div>
                    <span>Very Sad</span>
                </div>
                <div class="mood-option" data-mood="1">
                    <div class="mood-emoji">😐</div>
                    <span>Sad</span>
                </div>
                <div class="mood-option" data-mood="2">
                    <div class="mood-emoji">🙂</div>
                    <span>Okay</span>
                </div>
                <div class="mood-option" data-mood="3">
                    <div class="mood-emoji">😃</div>
                    <span>Happy</span>
                </div>
                <div class="mood-option" data-mood="4">
                    <div class="mood-emoji">🤩</div>
                    <span>Very Happy</span>
                </div>
            </div>
            <div class="modal-actions">
                <button id="save-mood-btn" class="primary-btn" disabled>Save Mood</button>
                <button id="cancel-mood-btn" class="secondary-btn">Cancel</button>
            </div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script>
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            loadDashboard();
            setupQuickActions();
        });

        function loadDashboard() {
            const userData = getUserData();
            if (!userData) {
                window.location.href = 'index.html';
                return;
            }

            // Update user info
            document.getElementById('username').textContent = userData.username;
            document.getElementById('level').textContent = userData.level;
            document.getElementById('xp-value').textContent = userData.xp;
            document.getElementById('streak-value').textContent = userData.streak;
            document.getElementById('total-days').textContent = userData.totalDays;
            document.getElementById('badge-count').textContent = userData.badges.length;

            // Update avatar
            const avatarElement = document.getElementById('user-avatar');
            avatarElement.className = `avatar-circle ${userData.avatar}-avatar`;
            avatarElement.textContent = getAvatarEmoji(userData.avatar);

            // Update current date
            document.getElementById('current-date').textContent = formatDate(getCurrentDate());

            // Update level progress
            updateLevelProgress(userData);

            // Update task statuses
            updateTaskStatuses(userData);

            // Update daily progress
            updateDailyProgress(userData);

            // Show recent badges
            showRecentBadges(userData);
        }

        function updateLevelProgress(userData) {
            const progress = getProgressToNextLevel();
            if (progress) {
                const progressBar = document.getElementById('level-progress-bar');
                const progressText = document.getElementById('level-progress-text');
                
                if (progress.isMaxLevel) {
                    progressBar.style.width = '100%';
                    progressBar.querySelector('.progress-text').textContent = 'MAX';
                    progressText.textContent = 'Maximum level reached!';
                } else {
                    progressBar.style.width = progress.progress + '%';
                    progressBar.querySelector('.progress-text').textContent = progress.progress + '%';
                    progressText.textContent = `${progress.currentXP} / ${progress.requiredXP} XP to Level ${progress.nextLevel}`;
                }
            }
        }

        function updateTaskStatuses(userData) {
            const tasks = ['breathing', 'journal', 'mood', 'meditation'];
            tasks.forEach(task => {
                const statusElement = document.getElementById(`${task}-status`);
                const indicator = statusElement.querySelector('.status-indicator');
                
                if (userData.dailyTasks[task].completed) {
                    indicator.textContent = 'Completed';
                    indicator.className = 'status-indicator completed';
                } else {
                    indicator.textContent = 'Pending';
                    indicator.className = 'status-indicator pending';
                }
            });
        }

        function updateDailyProgress(userData) {
            const completedTasks = Object.values(userData.dailyTasks).filter(task => task.completed).length;
            const totalTasks = 4;
            const progress = Math.round((completedTasks / totalTasks) * 100);
            
            const progressBar = document.getElementById('daily-progress-bar');
            const progressText = document.getElementById('daily-progress-text');
            
            progressBar.style.width = progress + '%';
            progressBar.querySelector('.progress-text').textContent = progress + '%';
            progressText.textContent = `${completedTasks} of ${totalTasks} tasks completed`;
        }

        function showRecentBadges(userData) {
            const badgeContainer = document.getElementById('recent-badges');
            
            if (userData.badges.length === 0) {
                badgeContainer.innerHTML = '<p class="no-badges">Complete tasks to earn your first badge!</p>';
                return;
            }

            const recentBadges = userData.badges.slice(-3); // Show last 3 badges
            badgeContainer.innerHTML = recentBadges.map(badgeId => {
                const badge = getBadge(badgeId);
                return `
                    <div class="badge-item">
                        <div class="badge-icon">${badge.icon}</div>
                        <div class="badge-name">${badge.name}</div>
                    </div>
                `;
            }).join('');
        }

        function setupQuickActions() {
            // Quick mood check
            document.getElementById('mood-quick-btn').addEventListener('click', showQuickMoodModal);
            document.getElementById('cancel-mood-btn').addEventListener('click', hideQuickMoodModal);
            document.getElementById('save-mood-btn').addEventListener('click', saveQuickMood);

            // Mood selection
            document.querySelectorAll('.mood-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.mood-option').forEach(opt => opt.classList.remove('selected'));
                    this.classList.add('selected');
                    document.getElementById('save-mood-btn').disabled = false;
                });
            });

            // Quick breathing
            document.getElementById('breathing-quick-btn').addEventListener('click', function() {
                window.location.href = 'tasks.html#breathing';
            });
        }

        function showQuickMoodModal() {
            document.getElementById('quick-mood-modal').classList.remove('hidden');
        }

        function hideQuickMoodModal() {
            document.getElementById('quick-mood-modal').classList.add('hidden');
            document.querySelectorAll('.mood-option').forEach(opt => opt.classList.remove('selected'));
            document.getElementById('save-mood-btn').disabled = true;
        }

        function saveQuickMood() {
            const selectedMood = document.querySelector('.mood-option.selected');
            if (selectedMood) {
                const moodValue = parseInt(selectedMood.dataset.mood);
                completeTaskWithRewards('mood', { value: moodValue });
                hideQuickMoodModal();
                loadDashboard(); // Refresh dashboard
            }
        }

        function getAvatarEmoji(avatarType) {
            const avatars = {
                peaceful: '🧘',
                nature: '🌱',
                strong: '💪',
                wise: '🦉',
                creative: '🎨',
                bright: '⭐'
            };
            return avatars[avatarType] || '🧘';
        }
    </script>

    <style>
        /* Dashboard specific styles */
        .dashboard-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-details h2 {
            margin: 0;
            color: white;
            font-size: 1.5rem;
        }

        .user-level {
            color: rgba(255,255,255,0.8);
            margin: 0;
        }

        .main-nav {
            display: flex;
            gap: 1rem;
        }

        .nav-link {
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            transition: background 0.3s ease;
        }

        .nav-link:hover, .nav-link.active {
            background: rgba(255,255,255,0.2);
        }

        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .task-preview {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(255,255,255,0.5);
            border-radius: 10px;
            transition: transform 0.3s ease;
        }

        .task-preview:hover {
            transform: translateY(-2px);
        }

        .task-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
        }

        .task-info h4 {
            margin: 0 0 0.25rem 0;
            color: #333;
        }

        .task-info p {
            margin: 0 0 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .task-xp {
            color: #667eea;
            font-weight: 600;
            font-size: 0.8rem;
        }

        .status-indicator {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-indicator.pending {
            background: #ffeaa7;
            color: #d63031;
        }

        .status-indicator.completed {
            background: #55a3ff;
            color: white;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.1);
            border: none;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
        }

        .action-btn:hover {
            background: rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .action-icon {
            font-size: 1.5rem;
        }

        .badge-showcase {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
            margin: 1rem 0;
        }

        .badge-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            background: rgba(255,255,255,0.5);
            border-radius: 10px;
            min-width: 80px;
        }

        .badge-icon {
            font-size: 2rem;
        }

        .badge-name {
            font-size: 0.8rem;
            text-align: center;
            font-weight: 600;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 2rem;
            border-radius: 20px;
            max-width: 500px;
            width: 90%;
        }

        .mood-selector {
            display: flex;
            gap: 1rem;
            margin: 1.5rem 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .mood-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .mood-option:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .mood-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .mood-emoji {
            font-size: 2rem;
        }

        .modal-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 1.5rem;
        }

        @media (max-width: 768px) {
            .dashboard-header {
                flex-direction: column;
                text-align: center;
            }

            .task-grid {
                grid-template-columns: 1fr;
            }

            .action-buttons {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script src="js/enhanced-features.js"></script>
</body>
</html>
