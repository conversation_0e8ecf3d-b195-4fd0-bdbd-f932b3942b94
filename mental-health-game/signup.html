<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign Up - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-elements">
                <div class="floating-circle circle-1"></div>
                <div class="floating-circle circle-2"></div>
                <div class="floating-circle circle-3"></div>
                <div class="floating-circle circle-4"></div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="auth-content">
            <!-- Header -->
            <header class="auth-header">
                <div class="app-logo">
                    <h1 class="app-title">🧘 MindJourney</h1>
                    <p class="app-subtitle">Begin Your Wellness Adventure</p>
                </div>
            </header>

            <!-- Sign Up Form -->
            <main class="auth-main">
                <div class="auth-card">
                    <div class="auth-card-header">
                        <h2>Create Your Account</h2>
                        <p>Start your journey to better mental wellness</p>
                    </div>

                    <form id="signup-form" class="auth-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="signup-firstname">First Name</label>
                                <input 
                                    type="text" 
                                    id="signup-firstname" 
                                    name="firstname" 
                                    placeholder="Enter your first name"
                                    required
                                    autocomplete="given-name"
                                >
                                <div class="input-icon">👤</div>
                            </div>

                            <div class="form-group">
                                <label for="signup-lastname">Last Name</label>
                                <input 
                                    type="text" 
                                    id="signup-lastname" 
                                    name="lastname" 
                                    placeholder="Enter your last name"
                                    required
                                    autocomplete="family-name"
                                >
                                <div class="input-icon">👤</div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="signup-email">Email Address</label>
                            <input 
                                type="email" 
                                id="signup-email" 
                                name="email" 
                                placeholder="Enter your email"
                                required
                                autocomplete="email"
                            >
                            <div class="input-icon">📧</div>
                        </div>

                        <div class="form-group">
                            <label for="signup-password">Password</label>
                            <input 
                                type="password" 
                                id="signup-password" 
                                name="password" 
                                placeholder="Create a strong password"
                                required
                                autocomplete="new-password"
                                minlength="8"
                            >
                            <div class="input-icon">🔒</div>
                            <button type="button" class="password-toggle" id="password-toggle">
                                <span class="toggle-icon">👁️</span>
                            </button>
                        </div>

                        <div class="password-strength" id="password-strength">
                            <div class="strength-bar">
                                <div class="strength-fill" id="strength-fill"></div>
                            </div>
                            <div class="strength-text" id="strength-text">Password strength</div>
                        </div>

                        <div class="form-group">
                            <label for="signup-confirm-password">Confirm Password</label>
                            <input 
                                type="password" 
                                id="signup-confirm-password" 
                                name="confirmPassword" 
                                placeholder="Confirm your password"
                                required
                                autocomplete="new-password"
                            >
                            <div class="input-icon">🔒</div>
                        </div>

                        <div class="form-group">
                            <label for="signup-birthdate">Date of Birth (Optional)</label>
                            <input 
                                type="date" 
                                id="signup-birthdate" 
                                name="birthdate"
                                autocomplete="bday"
                            >
                            <div class="input-icon">🎂</div>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="terms-agreement" name="terms" required>
                                <span class="checkmark"></span>
                                I agree to the <a href="#" class="terms-link">Terms of Service</a> and <a href="#" class="privacy-link">Privacy Policy</a>
                            </label>

                            <label class="checkbox-label">
                                <input type="checkbox" id="newsletter-signup" name="newsletter">
                                <span class="checkmark"></span>
                                Send me wellness tips and updates
                            </label>
                        </div>

                        <button type="submit" class="auth-btn primary" id="signup-btn">
                            <span class="btn-text">Create Account</span>
                            <span class="btn-loader hidden">
                                <div class="spinner"></div>
                            </span>
                        </button>

                        <div class="auth-divider">
                            <span>or</span>
                        </div>

                        <div class="social-signin">
                            <button type="button" class="social-btn google-btn" id="google-signup">
                                <span class="social-icon">🔍</span>
                                Sign up with Google
                            </button>
                            <button type="button" class="social-btn apple-btn" id="apple-signup">
                                <span class="social-icon">🍎</span>
                                Sign up with Apple
                            </button>
                        </div>
                    </form>

                    <div class="auth-footer">
                        <p>Already have an account? <a href="signin.html" class="auth-link">Sign in here</a></p>
                    </div>
                </div>

                <!-- Features Preview -->
                <div class="features-preview">
                    <h3>What You'll Get</h3>
                    <div class="features-list">
                        <div class="feature-item">
                            <div class="feature-icon">🥽</div>
                            <div class="feature-content">
                                <h4>VR Meditation</h4>
                                <p>Immersive 3D meditation experiences</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🤖</div>
                            <div class="feature-content">
                                <h4>AI Companion</h4>
                                <p>Personalized wellness guidance</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">📊</div>
                            <div class="feature-content">
                                <h4>Progress Tracking</h4>
                                <p>Monitor your wellness journey</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🏆</div>
                            <div class="feature-content">
                                <h4>Achievements</h4>
                                <p>Unlock badges and rewards</p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>

        <!-- Error/Success Messages -->
        <div id="auth-message" class="auth-message hidden">
            <div class="message-content">
                <span class="message-icon"></span>
                <span class="message-text"></span>
                <button class="message-close">&times;</button>
            </div>
        </div>

        <!-- Terms Modal -->
        <div id="terms-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Terms of Service</h3>
                    <button class="modal-close" id="close-terms-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="terms-content">
                        <h4>1. Acceptance of Terms</h4>
                        <p>By using MindJourney, you agree to these terms and conditions.</p>
                        
                        <h4>2. Privacy & Data</h4>
                        <p>Your wellness data is stored locally on your device. We do not collect or share personal information.</p>
                        
                        <h4>3. AI Features</h4>
                        <p>AI features require an OpenAI API key. Your conversations are processed by OpenAI according to their privacy policy.</p>
                        
                        <h4>4. VR Safety</h4>
                        <p>Use VR features in a safe environment. Take breaks if you experience discomfort.</p>
                        
                        <h4>5. Medical Disclaimer</h4>
                        <p>MindJourney is for wellness purposes only and is not a substitute for professional medical advice.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Privacy Modal -->
        <div id="privacy-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Privacy Policy</h3>
                    <button class="modal-close" id="close-privacy-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="privacy-content">
                        <h4>Data Collection</h4>
                        <p>We collect minimal data necessary for app functionality. All personal wellness data is stored locally.</p>
                        
                        <h4>Data Usage</h4>
                        <p>Your data is used only to provide personalized wellness experiences within the app.</p>
                        
                        <h4>Third-Party Services</h4>
                        <p>OpenAI API is used for AI features. No personal data is permanently stored by OpenAI.</p>
                        
                        <h4>Data Security</h4>
                        <p>We implement industry-standard security measures to protect your information.</p>
                        
                        <h4>Your Rights</h4>
                        <p>You can export, modify, or delete your data at any time through the app settings.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Demo Mode Banner -->
        <div class="demo-banner">
            <div class="demo-content">
                <span class="demo-icon">🚀</span>
                <span class="demo-text">Want to try first?</span>
                <a href="signin.html" class="demo-link">Continue as Guest</a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize sign up page
        document.addEventListener('DOMContentLoaded', function() {
            initializeSignUp();
        });

        function initializeSignUp() {
            // Check if user is already signed in
            const currentUser = Auth.getCurrentUser();
            if (currentUser) {
                window.location.href = 'dashboard.html';
                return;
            }

            // Setup form handlers
            setupSignUpForm();
            setupPasswordToggle();
            setupPasswordStrength();
            setupModals();
            setupSocialSignUp();
        }

        function setupSignUpForm() {
            const form = document.getElementById('signup-form');
            const submitBtn = document.getElementById('signup-btn');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(form);
                const userData = {
                    firstName: formData.get('firstname'),
                    lastName: formData.get('lastname'),
                    email: formData.get('email'),
                    password: formData.get('password'),
                    confirmPassword: formData.get('confirmPassword'),
                    birthdate: formData.get('birthdate'),
                    newsletter: formData.get('newsletter') === 'on'
                };

                // Validate form
                if (!validateSignUpForm(userData)) {
                    return;
                }

                // Show loading state
                showButtonLoading(submitBtn, true);

                try {
                    const result = await Auth.signUp(userData);
                    
                    if (result.success) {
                        showMessage('success', 'Account created successfully! Redirecting to onboarding...');
                        
                        setTimeout(() => {
                            window.location.href = 'onboarding.html';
                        }, 1500);
                    } else {
                        showMessage('error', result.message || 'Sign up failed. Please try again.');
                    }
                } catch (error) {
                    showMessage('error', 'An error occurred. Please try again.');
                    console.error('Sign up error:', error);
                } finally {
                    showButtonLoading(submitBtn, false);
                }
            });
        }

        function validateSignUpForm(userData) {
            // Check password match
            if (userData.password !== userData.confirmPassword) {
                showMessage('error', 'Passwords do not match.');
                return false;
            }

            // Check password strength
            const strength = calculatePasswordStrength(userData.password);
            if (strength < 3) {
                showMessage('error', 'Please choose a stronger password.');
                return false;
            }

            // Check terms agreement
            if (!document.getElementById('terms-agreement').checked) {
                showMessage('error', 'Please agree to the Terms of Service.');
                return false;
            }

            return true;
        }

        function setupPasswordToggle() {
            const toggle = document.getElementById('password-toggle');
            const passwordInput = document.getElementById('signup-password');

            toggle.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = toggle.querySelector('.toggle-icon');
                icon.textContent = type === 'password' ? '👁️' : '🙈';
            });
        }

        function setupPasswordStrength() {
            const passwordInput = document.getElementById('signup-password');
            const strengthBar = document.getElementById('strength-fill');
            const strengthText = document.getElementById('strength-text');

            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                
                updatePasswordStrengthUI(strength, strengthBar, strengthText);
            });
        }

        function calculatePasswordStrength(password) {
            let strength = 0;
            
            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;
            
            return strength;
        }

        function updatePasswordStrengthUI(strength, bar, text) {
            const levels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
            const colors = ['#ff4757', '#ff6b7a', '#ffa502', '#2ed573', '#1dd1a1'];
            
            const level = Math.min(strength, 4);
            const percentage = (level + 1) * 20;
            
            bar.style.width = percentage + '%';
            bar.style.backgroundColor = colors[level];
            text.textContent = levels[level] || 'Very Weak';
            text.style.color = colors[level];
        }

        function setupModals() {
            // Terms modal
            const termsLink = document.querySelector('.terms-link');
            const termsModal = document.getElementById('terms-modal');
            const closeTerms = document.getElementById('close-terms-modal');

            termsLink.addEventListener('click', function(e) {
                e.preventDefault();
                termsModal.classList.remove('hidden');
            });

            closeTerms.addEventListener('click', function() {
                termsModal.classList.add('hidden');
            });

            // Privacy modal
            const privacyLink = document.querySelector('.privacy-link');
            const privacyModal = document.getElementById('privacy-modal');
            const closePrivacy = document.getElementById('close-privacy-modal');

            privacyLink.addEventListener('click', function(e) {
                e.preventDefault();
                privacyModal.classList.remove('hidden');
            });

            closePrivacy.addEventListener('click', function() {
                privacyModal.classList.add('hidden');
            });
        }

        function setupSocialSignUp() {
            document.getElementById('google-signup').addEventListener('click', function() {
                showMessage('info', 'Google Sign-Up coming soon!');
            });

            document.getElementById('apple-signup').addEventListener('click', function() {
                showMessage('info', 'Apple Sign-Up coming soon!');
            });
        }

        function showButtonLoading(button, loading) {
            const text = button.querySelector('.btn-text');
            const loader = button.querySelector('.btn-loader');
            
            if (loading) {
                text.classList.add('hidden');
                loader.classList.remove('hidden');
                button.disabled = true;
            } else {
                text.classList.remove('hidden');
                loader.classList.add('hidden');
                button.disabled = false;
            }
        }

        function showMessage(type, message) {
            const messageEl = document.getElementById('auth-message');
            const icon = messageEl.querySelector('.message-icon');
            const text = messageEl.querySelector('.message-text');
            const close = messageEl.querySelector('.message-close');

            text.textContent = message;
            
            const icons = {
                success: '✅',
                error: '❌',
                info: 'ℹ️',
                warning: '⚠️'
            };
            icon.textContent = icons[type] || 'ℹ️';

            messageEl.className = `auth-message ${type}`;
            messageEl.classList.remove('hidden');

            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 5000);

            close.onclick = () => messageEl.classList.add('hidden');
        }
    </script>
</body>
</html>
