/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* Typography */
h1, h2, h3 {
    margin-bottom: 1rem;
}

.app-title {
    font-size: 3rem;
    font-weight: 700;
    color: white;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    margin-bottom: 0.5rem;
}

.app-subtitle {
    font-size: 1.2rem;
    color: rgba(255,255,255,0.9);
    text-align: center;
    margin-bottom: 2rem;
}

/* Cards and Containers */
.setup-card, .welcome-back-card, .dashboard-card, .task-card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    margin-bottom: 2rem;
}

/* Buttons */
.primary-btn, .secondary-btn, .task-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
}

.primary-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.primary-btn:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.primary-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.secondary-btn {
    background: transparent;
    color: #667eea;
    border: 2px solid #667eea;
}

.secondary-btn:hover {
    background: #667eea;
    color: white;
}

.task-btn {
    background: linear-gradient(45deg, #4facfe, #00f2fe);
    color: white;
    width: 100%;
    margin-top: 1rem;
}

.task-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.4);
}

.task-btn.completed {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
}

/* Avatar Styles */
.avatar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.avatar-option {
    text-align: center;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.avatar-option:hover {
    transform: scale(1.05);
}

.avatar-option.selected .avatar-circle {
    border: 3px solid #667eea;
    box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
}

.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 0.5rem;
    border: 3px solid transparent;
    transition: all 0.3s ease;
}

.peaceful-avatar { background: linear-gradient(45deg, #a8edea, #fed6e3); }
.nature-avatar { background: linear-gradient(45deg, #d299c2, #fef9d7); }
.strong-avatar { background: linear-gradient(45deg, #89f7fe, #66a6ff); }
.wise-avatar { background: linear-gradient(45deg, #fdbb2d, #22c1c3); }
.creative-avatar { background: linear-gradient(45deg, #ff9a9e, #fecfef); }
.bright-avatar { background: linear-gradient(45deg, #ffecd2, #fcb69f); }

/* Input Styles */
.input-group {
    margin-bottom: 1.5rem;
}

.input-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #555;
}

.input-group input, .input-group textarea, .input-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus, .input-group textarea:focus, .input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Stats Display */
.quick-stats, .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255,255,255,0.5);
    border-radius: 10px;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: #667eea;
}

/* Progress Bars */
.progress-container {
    background: rgba(255,255,255,0.3);
    border-radius: 10px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    height: 20px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.8rem;
    font-weight: 600;
}

/* Utility Classes */
.hidden { display: none !important; }
.text-center { text-align: center; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .app-title {
        font-size: 2rem;
    }
    
    .setup-card, .welcome-back-card, .dashboard-card, .task-card {
        padding: 1.5rem;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .avatar-circle {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: 1.5rem;
    }
    
    .setup-card, .welcome-back-card, .dashboard-card, .task-card {
        padding: 1rem;
    }
    
    .avatar-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Task Page Specific Styles */
.task-container {
    max-width: 800px;
    margin: 0 auto;
}

.task-navigation {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.task-nav-btn {
    padding: 10px 20px;
    border: 2px solid #667eea;
    background: transparent;
    color: #667eea;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.task-nav-btn:hover, .task-nav-btn.active {
    background: #667eea;
    color: white;
}

.task-section {
    margin-bottom: 3rem;
}

.task-section.hidden {
    display: none;
}

.breathing-container {
    text-align: center;
}

.breathing-instructions {
    margin-bottom: 2rem;
}

.breathing-instruction {
    font-size: 1.2rem;
    font-weight: 600;
    color: #667eea;
    margin-top: 1rem;
}

.journal-container textarea {
    min-height: 150px;
    resize: vertical;
}

.mood-tracker {
    text-align: center;
}

.mood-grid {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.mood-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 3px solid transparent;
    min-width: 80px;
}

.mood-item:hover {
    background: rgba(102, 126, 234, 0.1);
}

.mood-item.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.mood-emoji {
    font-size: 3rem;
}

.mood-label {
    font-weight: 600;
    color: #333;
}

.meditation-container {
    text-align: center;
}

.timer-display {
    font-size: 4rem;
    font-weight: 700;
    color: #667eea;
    margin: 2rem 0;
    font-family: 'Courier New', monospace;
}

.timer-controls {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin: 2rem 0;
}

.timer-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.timer-btn.start {
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
}

.timer-btn.stop {
    background: linear-gradient(45deg, #ff6b6b, #ffa8a8);
    color: white;
}

.task-complete-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    background: linear-gradient(45deg, #56ab2f, #a8e6cf);
    color: white;
    border-radius: 10px;
    margin-top: 1rem;
    font-weight: 600;
}

.task-complete-indicator.hidden {
    display: none;
}
