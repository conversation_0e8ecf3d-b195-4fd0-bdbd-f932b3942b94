/* Floating Background Elements */
.floating-elements {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.floating-circle {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.circle-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.circle-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-delay: 2s;
}

.circle-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 50%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.6;
    }
}

/* Breathing Animation */
.breathing-circle {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    margin: 2rem auto;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    font-weight: 600;
    position: relative;
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.3);
}

.breathing-circle.inhale {
    animation: breatheIn 4s ease-in-out;
}

.breathing-circle.hold {
    animation: breatheHold 4s ease-in-out;
}

.breathing-circle.exhale {
    animation: breatheOut 6s ease-in-out;
}

@keyframes breatheIn {
    0% { transform: scale(1); }
    100% { transform: scale(1.3); }
}

@keyframes breatheHold {
    0%, 100% { transform: scale(1.3); }
}

@keyframes breatheOut {
    0% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

/* Pulse Animation for Interactive Elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Confetti Animation */
.confetti-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1000;
}

.confetti-piece {
    position: absolute;
    width: 10px;
    height: 10px;
    background: #667eea;
    animation: confettiFall 3s linear forwards;
}

.confetti-piece:nth-child(2n) { background: #764ba2; }
.confetti-piece:nth-child(3n) { background: #4facfe; }
.confetti-piece:nth-child(4n) { background: #00f2fe; }
.confetti-piece:nth-child(5n) { background: #56ab2f; }

@keyframes confettiFall {
    0% {
        transform: translateY(-100vh) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}

/* Glow Effect for Rewards */
.glow {
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from {
        box-shadow: 0 0 20px rgba(102, 126, 234, 0.5);
    }
    to {
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.8), 0 0 40px rgba(102, 126, 234, 0.6);
    }
}

/* Slide In Animations */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-up {
    animation: slideInUp 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Fade In Animation */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Bounce Animation for Achievements */
.bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    80% {
        transform: translateY(-10px);
    }
}

/* Progress Bar Fill Animation */
.progress-fill {
    animation: progressFill 1s ease-out;
}

@keyframes progressFill {
    from { width: 0%; }
}

/* Typing Animation for Chat Bot */
.typing-indicator {
    display: inline-block;
}

.typing-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    margin: 0 2px;
    animation: typingDot 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingDot {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Shake Animation for Errors */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Mood Selector Animation */
.mood-option {
    transition: all 0.3s ease;
    cursor: pointer;
}

.mood-option:hover {
    transform: scale(1.1);
}

.mood-option.selected {
    transform: scale(1.2);
    animation: moodSelected 0.5s ease-out;
}

@keyframes moodSelected {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1.2); }
}

/* Badge Unlock Animation */
.badge-unlock {
    animation: badgeUnlock 1s ease-out;
}

@keyframes badgeUnlock {
    0% {
        transform: scale(0) rotate(-180deg);
        opacity: 0;
    }
    50% {
        transform: scale(1.2) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }
}

/* Meditation Timer Animation */
.meditation-bg {
    background: linear-gradient(45deg, #1e3c72, #2a5298);
    transition: background 2s ease-in-out;
}

.meditation-bg.sunrise {
    background: linear-gradient(45deg, #ff9a9e, #fecfef, #fecfef);
}

/* Responsive Animations */
@media (max-width: 768px) {
    .breathing-circle {
        width: 150px;
        height: 150px;
        font-size: 1rem;
    }
    
    .floating-circle {
        display: none;
    }
}
