/* VR Meditation Styles */

.vr-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow: hidden;
}

/* VR Notification */
.vr-notification {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.vr-notification-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.vr-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

.vr-notification h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.vr-notification p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.vr-notification-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
}

/* VR Container */
.vr-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

#vr-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* VR UI Overlay */
.vr-ui {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 100;
}

.vr-ui.meditation-mode .vr-controls {
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.5s ease;
}

.vr-controls {
    position: absolute;
    top: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    padding: 2rem;
    border-radius: 15px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    pointer-events: auto;
    max-width: 350px;
}

.vr-controls h3 {
    color: white;
    margin-bottom: 1rem;
    font-size: 1.2rem;
}

/* Environment Selector */
.environment-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-bottom: 2rem;
}

.env-btn {
    padding: 0.8rem;
    border: 2px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.env-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.env-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-color: #667eea;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* Meditation Controls */
.duration-selector {
    margin-bottom: 1rem;
}

.duration-selector label {
    color: white;
    display: block;
    margin-bottom: 0.5rem;
}

.duration-selector select {
    width: 100%;
    padding: 0.8rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    font-size: 1rem;
}

.audio-controls {
    margin-bottom: 2rem;
}

.audio-controls label {
    display: flex;
    align-items: center;
    color: white;
    margin-bottom: 0.8rem;
    cursor: pointer;
}

.audio-controls input[type="checkbox"] {
    margin-right: 0.8rem;
    transform: scale(1.2);
}

/* Session Controls */
.session-controls {
    text-align: center;
}

.primary-btn.large {
    padding: 1rem 2rem;
    font-size: 1.2rem;
    width: 100%;
}

/* VR Timer */
.vr-timer {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    pointer-events: none;
}

.timer-circle {
    width: 200px;
    height: 200px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

#vr-time-display {
    font-size: 3rem;
    color: white;
    font-weight: bold;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.meditation-phase {
    color: white;
    font-size: 1.5rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    animation: pulse 2s ease-in-out infinite;
}

/* VR Breathing Guide */
.vr-breathing-guide {
    position: absolute;
    bottom: 100px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    pointer-events: none;
}

.breathing-orb {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.8), rgba(102, 126, 234, 0.6));
    margin: 0 auto 1rem;
    animation: breathe 8s ease-in-out infinite;
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
}

.breathing-text {
    color: white;
    font-size: 1.2rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

/* Desktop Fallback */
.desktop-fallback {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    max-width: 600px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.fallback-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.fallback-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.fallback-instructions {
    text-align: left;
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 10px;
    margin-top: 2rem;
}

.fallback-instructions h3 {
    color: white;
    margin-bottom: 1rem;
}

.fallback-instructions ul {
    color: rgba(255, 255, 255, 0.9);
    list-style: none;
    padding: 0;
}

.fallback-instructions li {
    margin-bottom: 0.8rem;
    padding-left: 1rem;
}

/* Loading Screen */
.vr-loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 2rem;
}

.loading-content h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

#loading-status {
    opacity: 0.8;
    font-size: 1.1rem;
}

/* Session Complete */
.session-complete {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(10px);
}

.complete-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
    padding: 3rem;
    border-radius: 20px;
    text-align: center;
    max-width: 500px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.1);
}

.complete-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: bounce 2s ease-in-out infinite;
}

.complete-content h2 {
    color: white;
    margin-bottom: 1rem;
}

.complete-content p {
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 2rem;
}

.session-stats {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: white;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    margin-top: 0.5rem;
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes breathe {
    0%, 100% { transform: scale(1); opacity: 0.8; }
    50% { transform: scale(1.3); opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .vr-controls {
        position: relative;
        top: auto;
        left: auto;
        margin: 1rem;
        max-width: none;
    }
    
    .environment-options {
        grid-template-columns: 1fr;
    }
    
    .timer-circle {
        width: 150px;
        height: 150px;
    }
    
    #vr-time-display {
        font-size: 2rem;
    }
    
    .session-stats {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Hidden utility class */
.hidden {
    display: none !important;
}
