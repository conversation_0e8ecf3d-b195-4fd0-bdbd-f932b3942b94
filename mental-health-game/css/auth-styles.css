/* Authentication Pages Styles */

.auth-page {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

/* Background */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: -1;
}

.auth-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

/* Container */
.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem 1rem;
    position: relative;
}

.auth-content {
    width: 100%;
    max-width: 1200px;
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: start;
}

/* Header */
.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.app-logo .app-title {
    font-size: 3rem;
    color: white;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    animation: float 3s ease-in-out infinite;
}

.app-logo .app-subtitle {
    font-size: 1.2rem;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.auth-main {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    align-items: start;
}

/* Auth Card */
.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    margin: 0 auto;
    width: 100%;
}

.auth-card-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-card-header h2 {
    font-size: 2rem;
    color: #333;
    margin: 0 0 0.5rem 0;
}

.auth-card-header p {
    color: #666;
    margin: 0;
    font-size: 1.1rem;
}

/* Form Styles */
.auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-group {
    position: relative;
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group input {
    padding: 1rem 1rem 1rem 3rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 2.2rem;
    font-size: 1.2rem;
    color: #666;
    pointer-events: none;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 2.2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    color: #666;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Password Strength */
.password-strength {
    margin-top: 0.5rem;
}

.strength-bar {
    width: 100%;
    height: 4px;
    background: #e1e8ed;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 2px;
}

.strength-text {
    font-size: 0.8rem;
    font-weight: 600;
}

/* Form Options */
.form-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #333;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #e1e8ed;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 0.8rem;
    font-weight: bold;
}

.forgot-password {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    margin-left: auto;
}

.forgot-password:hover {
    text-decoration: underline;
}

/* Buttons */
.auth-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.auth-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.auth-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.auth-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none !important;
}

.btn-loader {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Divider */
.auth-divider {
    text-align: center;
    position: relative;
    margin: 1rem 0;
}

.auth-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e1e8ed;
}

.auth-divider span {
    background: white;
    padding: 0 1rem;
    color: #666;
    font-size: 0.9rem;
    position: relative;
}

/* Social Buttons */
.social-signin {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.social-btn {
    padding: 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    background: white;
    color: #333;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
}

.social-btn:hover {
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.social-icon {
    font-size: 1.2rem;
}

/* Auth Footer */
.auth-footer {
    text-align: center;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid #e1e8ed;
}

.auth-footer p {
    color: #666;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.auth-link:hover {
    text-decoration: underline;
}

/* Quick Access */
.quick-access {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 400px;
    margin: 0 auto;
}

.quick-access h3 {
    color: white;
    text-align: center;
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
}

.quick-access-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.quick-access-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    text-decoration: none;
    color: white;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.quick-access-item:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.quick-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.quick-access-item span {
    font-size: 0.9rem;
    text-align: center;
    font-weight: 600;
}

/* Features Preview */
.features-preview {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 500px;
    margin: 0 auto;
}

.features-preview h3 {
    color: white;
    text-align: center;
    margin: 0 0 1.5rem 0;
    font-size: 1.3rem;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.feature-item .feature-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.feature-content h4 {
    color: white;
    margin: 0 0 0.3rem 0;
    font-size: 1rem;
}

.feature-content p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    font-size: 0.9rem;
}

/* Messages */
.auth-message {
    position: fixed;
    top: 2rem;
    right: 2rem;
    max-width: 400px;
    z-index: 1000;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideIn 0.3s ease;
}

.auth-message.success {
    background: rgba(46, 213, 115, 0.9);
    color: white;
}

.auth-message.error {
    background: rgba(255, 71, 87, 0.9);
    color: white;
}

.auth-message.info {
    background: rgba(102, 126, 234, 0.9);
    color: white;
}

.auth-message.warning {
    background: rgba(255, 165, 2, 0.9);
    color: white;
}

.message-content {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.message-icon {
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message-text {
    flex: 1;
    font-weight: 500;
}

.message-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.message-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 2rem 2rem 1rem 2rem;
    border-bottom: 1px solid #e1e8ed;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.2s ease;
}

.modal-close:hover {
    background: #f0f0f0;
}

.modal-body {
    padding: 2rem;
    overflow-y: auto;
    max-height: 60vh;
}

.terms-content h4,
.privacy-content h4 {
    color: #333;
    margin: 1.5rem 0 0.5rem 0;
    font-size: 1.1rem;
}

.terms-content h4:first-child,
.privacy-content h4:first-child {
    margin-top: 0;
}

.terms-content p,
.privacy-content p {
    color: #666;
    line-height: 1.6;
    margin: 0 0 1rem 0;
}

/* Demo Banner */
.demo-banner {
    position: fixed;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 50px;
    padding: 1rem 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 100;
}

.demo-content {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: white;
    font-size: 0.9rem;
}

.demo-icon {
    font-size: 1.2rem;
}

.demo-link {
    color: white;
    text-decoration: none;
    font-weight: 600;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    transition: all 0.3s ease;
}

.demo-link:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

/* Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (min-width: 768px) {
    .auth-content {
        grid-template-columns: 1fr 1fr;
        gap: 3rem;
        align-items: center;
    }
    
    .auth-main {
        grid-template-columns: 1fr;
    }
    
    .quick-access,
    .features-preview {
        margin: 0;
    }
}

@media (max-width: 768px) {
    .auth-container {
        padding: 1rem;
    }
    
    .auth-card {
        padding: 2rem 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .quick-access-grid {
        grid-template-columns: 1fr;
    }
    
    .auth-message {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
    
    .demo-banner {
        bottom: 1rem;
        left: 1rem;
        right: 1rem;
        transform: none;
    }
    
    .demo-content {
        justify-content: center;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .app-logo .app-title {
        font-size: 2.5rem;
    }
    
    .auth-card {
        padding: 1.5rem 1rem;
    }
    
    .social-signin {
        gap: 0.5rem;
    }
    
    .social-btn {
        padding: 0.8rem;
        font-size: 0.9rem;
    }
}

/* Onboarding Specific Styles */
.onboarding-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.onboarding-container {
    min-height: 100vh;
    padding: 2rem;
    position: relative;
}

.onboarding-progress {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    padding: 1rem 2rem;
    z-index: 100;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 3px;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    color: white;
    font-size: 0.9rem;
    font-weight: 600;
}

.onboarding-step {
    display: none;
    min-height: calc(100vh - 120px);
    padding-top: 120px;
    align-items: center;
    justify-content: center;
}

.onboarding-step.active {
    display: flex;
}

.step-content {
    max-width: 800px;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.step-header {
    text-align: center;
    margin-bottom: 3rem;
}

.step-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    animation: float 3s ease-in-out infinite;
}

.step-header h1,
.step-header h2 {
    color: #333;
    margin: 0 0 1rem 0;
}

.step-header h1 {
    font-size: 2.5rem;
}

.step-header h2 {
    font-size: 2rem;
}

.step-header p {
    color: #666;
    font-size: 1.2rem;
    margin: 0;
}

.welcome-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.feature-highlight {
    text-align: center;
    padding: 2rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.feature-highlight .feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-highlight h3 {
    color: #333;
    margin: 0 0 1rem 0;
}

.feature-highlight p {
    color: #666;
    margin: 0;
}

.checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.checkbox-card {
    display: block;
    cursor: pointer;
}

.checkbox-card input[type="checkbox"] {
    display: none;
}

.checkbox-card .card-content {
    padding: 1.5rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.checkbox-card input[type="checkbox"]:checked + .card-content {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.checkbox-card .card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.radio-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.radio-card {
    display: block;
    cursor: pointer;
}

.radio-card input[type="radio"] {
    display: none;
}

.radio-card .card-content {
    padding: 1.5rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.radio-card input[type="radio"]:checked + .card-content {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.radio-card .card-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.radio-card small {
    display: block;
    color: #666;
    font-size: 0.8rem;
    margin-top: 0.3rem;
}

.duration-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.duration-card {
    display: block;
    cursor: pointer;
}

.duration-card input[type="radio"] {
    display: none;
}

.duration-card .card-content {
    padding: 1rem;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    background: white;
}

.duration-card input[type="radio"]:checked + .card-content {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.duration-time {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.3rem;
}

.duration-label {
    display: block;
    font-size: 0.8rem;
    color: #666;
}

.toggle-options {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 1rem;
}

.toggle-option {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    width: 50px;
    height: 26px;
    background: #e1e8ed;
    border-radius: 13px;
    position: relative;
    transition: background 0.3s ease;
}

.toggle-slider::after {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 22px;
    height: 22px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.toggle-option input[type="checkbox"]:checked + .toggle-slider {
    background: #667eea;
}

.toggle-option input[type="checkbox"]:checked + .toggle-slider::after {
    transform: translateX(24px);
}

.toggle-option input[type="checkbox"] {
    display: none;
}

.toggle-label {
    color: #333;
    font-weight: 500;
}

.step-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 3rem;
}

.onboarding-btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
}

.onboarding-btn.primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.onboarding-btn.primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.onboarding-btn.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.onboarding-btn.secondary:hover {
    background: #667eea;
    color: white;
}

.onboarding-btn.large {
    padding: 1.5rem 3rem;
    font-size: 1.2rem;
}

.vr-setup-content,
.ai-setup-content {
    text-align: center;
}

.vr-status {
    padding: 2rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 15px;
    margin-bottom: 2rem;
}

.status-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.vr-options {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.option-card {
    padding: 2rem;
    background: white;
    border: 2px solid #e1e8ed;
    border-radius: 15px;
    text-align: center;
}

.option-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.option-btn {
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.option-btn.primary {
    background: #667eea;
    color: white;
}

.option-btn.secondary {
    background: white;
    color: #667eea;
    border: 2px solid #667eea;
}

.vr-info {
    background: rgba(255, 255, 255, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    text-align: left;
}

.vr-info h4 {
    color: #333;
    margin: 0 0 1rem 0;
}

.vr-info ul {
    color: #666;
    margin: 0;
    padding-left: 1.5rem;
}

.ai-explanation {
    background: rgba(102, 126, 234, 0.1);
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
    text-align: left;
}

.ai-explanation h3 {
    color: #333;
    margin: 0 0 1rem 0;
}

.ai-explanation ul {
    color: #666;
    margin: 0;
    padding-left: 1.5rem;
}

.ai-privacy {
    background: rgba(46, 213, 115, 0.1);
    padding: 1.5rem;
    border-radius: 12px;
    margin-top: 1rem;
    text-align: left;
}

.ai-privacy h4 {
    color: #333;
    margin: 0 0 1rem 0;
}

.ai-privacy ul {
    color: #666;
    margin: 0;
    padding-left: 1.5rem;
}

.completion-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 3rem;
}

.summary-card {
    background: rgba(102, 126, 234, 0.1);
    padding: 2rem;
    border-radius: 15px;
}

.summary-card h3 {
    color: #333;
    margin: 0 0 1.5rem 0;
}

.summary-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.2);
    color: #666;
}

.summary-item:last-child {
    border-bottom: none;
}

.next-steps h3 {
    color: #333;
    margin: 0 0 1.5rem 0;
}

.recommendation-cards {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 2px solid #e1e8ed;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.recommendation-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.rec-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.recommendation-card h4 {
    color: #333;
    margin: 0 0 0.3rem 0;
    font-size: 1rem;
}

.recommendation-card p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

.rec-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    margin-left: auto;
    padding: 0.5rem 1rem;
    border: 1px solid #667eea;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.rec-link:hover {
    background: #667eea;
    color: white;
}

/* VR Meditation Task Styles */
.vr-meditation-container {
    text-align: center;
}

.vr-preview {
    margin-bottom: 2rem;
}

.vr-preview-image {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 3rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.preview-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.vr-preview-image h3 {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
}

.vr-preview-image p {
    margin: 0;
    opacity: 0.9;
}

.vr-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    font-weight: 500;
    color: #333;
}

.feature-icon {
    font-size: 1.5rem;
}

.vr-actions {
    margin-top: 2rem;
}

.vr-note {
    margin-top: 1rem;
    color: #667eea;
    font-weight: 600;
}

/* Responsive Design for Onboarding */
@media (max-width: 768px) {
    .onboarding-container {
        padding: 1rem;
    }

    .step-content {
        padding: 2rem 1.5rem;
    }

    .checkbox-grid,
    .radio-cards {
        grid-template-columns: 1fr;
    }

    .duration-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .vr-options {
        grid-template-columns: 1fr;
    }

    .completion-summary {
        grid-template-columns: 1fr;
    }

    .step-actions {
        flex-direction: column;
    }

    .onboarding-btn {
        width: 100%;
    }
}

/* Hidden utility class */
.hidden {
    display: none !important;
}
