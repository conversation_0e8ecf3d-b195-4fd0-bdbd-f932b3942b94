<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sleep Tracker - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="sleep-page">
    <div class="container">
        <header class="page-header">
            <h1>😴 Sleep Wellness Tracker</h1>
            <p>Track and optimize your sleep for better mental health</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="sleep-tracker.html" class="nav-link active">Sleep</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="sleep-content">
            <!-- Sleep Entry -->
            <section class="sleep-section">
                <div class="dashboard-card">
                    <h2>🌙 Log Your Sleep</h2>
                    <div class="sleep-entry-form">
                        <div class="sleep-time-inputs">
                            <div class="input-group">
                                <label for="bedtime">Bedtime:</label>
                                <input type="time" id="bedtime" value="22:00">
                            </div>
                            <div class="input-group">
                                <label for="wake-time">Wake Time:</label>
                                <input type="time" id="wake-time" value="07:00">
                            </div>
                            <div class="input-group">
                                <label for="sleep-date">Date:</label>
                                <input type="date" id="sleep-date">
                            </div>
                        </div>

                        <div class="sleep-quality-section">
                            <h3>Sleep Quality Rating</h3>
                            <div class="quality-selector">
                                <div class="quality-option" data-quality="1">
                                    <div class="quality-emoji">😴</div>
                                    <span>Poor</span>
                                </div>
                                <div class="quality-option" data-quality="2">
                                    <div class="quality-emoji">😐</div>
                                    <span>Fair</span>
                                </div>
                                <div class="quality-option" data-quality="3">
                                    <div class="quality-emoji">😊</div>
                                    <span>Good</span>
                                </div>
                                <div class="quality-option" data-quality="4">
                                    <div class="quality-emoji">😄</div>
                                    <span>Great</span>
                                </div>
                                <div class="quality-option" data-quality="5">
                                    <div class="quality-emoji">🤩</div>
                                    <span>Excellent</span>
                                </div>
                            </div>
                        </div>

                        <div class="sleep-factors">
                            <h3>Sleep Factors (Optional)</h3>
                            <div class="factors-grid">
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="caffeine">
                                    <span>☕ Late Caffeine</span>
                                </label>
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="screen">
                                    <span>📱 Screen Time</span>
                                </label>
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="exercise">
                                    <span>🏃 Exercise</span>
                                </label>
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="stress">
                                    <span>😰 Stress</span>
                                </label>
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="alcohol">
                                    <span>🍷 Alcohol</span>
                                </label>
                                <label class="factor-checkbox">
                                    <input type="checkbox" name="factors" value="meditation">
                                    <span>🧘 Meditation</span>
                                </label>
                            </div>
                        </div>

                        <div class="sleep-notes">
                            <label for="sleep-notes">Sleep Notes (Optional):</label>
                            <textarea id="sleep-notes" placeholder="How did you feel? Any dreams? What affected your sleep?" maxlength="200"></textarea>
                        </div>

                        <button id="log-sleep" class="primary-btn">Log Sleep Entry</button>
                    </div>
                </div>
            </section>

            <!-- Sleep Statistics -->
            <section class="sleep-section">
                <div class="dashboard-card">
                    <h2>📊 Sleep Statistics</h2>
                    <div class="sleep-stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">⏰</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-duration">0h 0m</span>
                                <span class="stat-label">Average Duration</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">⭐</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-quality">0.0</span>
                                <span class="stat-label">Average Quality</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🛏️</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-bedtime">--:--</span>
                                <span class="stat-label">Average Bedtime</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🌅</div>
                            <div class="stat-info">
                                <span class="stat-number" id="avg-waketime">--:--</span>
                                <span class="stat-label">Average Wake Time</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sleep Trends -->
            <section class="sleep-section">
                <div class="dashboard-card">
                    <h2>📈 Sleep Trends</h2>
                    <div class="chart-container">
                        <canvas id="sleep-chart" width="400" height="200"></canvas>
                    </div>
                    <div class="trend-insights" id="sleep-insights">
                        <p>Log more sleep entries to see trends and insights!</p>
                    </div>
                </div>
            </section>

            <!-- AI Sleep Analysis -->
            <section class="sleep-section">
                <div class="dashboard-card ai-sleep-card">
                    <h2>🤖 AI Sleep Analysis</h2>
                    <div class="ai-analysis-container" id="sleep-ai-analysis">
                        <div class="analysis-placeholder">
                            <p>AI will analyze your sleep patterns and provide personalized recommendations.</p>
                        </div>
                    </div>
                    <button id="analyze-sleep" class="primary-btn">Generate Sleep Analysis</button>
                </div>
            </section>

            <!-- Sleep Recommendations -->
            <section class="sleep-section">
                <div class="dashboard-card">
                    <h2>💡 Sleep Optimization Tips</h2>
                    <div class="recommendations-container" id="sleep-recommendations">
                        <div class="tip-card">
                            <h4>🌙 Consistent Schedule</h4>
                            <p>Try to go to bed and wake up at the same time every day, even on weekends.</p>
                        </div>
                        <div class="tip-card">
                            <h4>📱 Digital Sunset</h4>
                            <p>Avoid screens 1 hour before bedtime to improve sleep quality.</p>
                        </div>
                        <div class="tip-card">
                            <h4>🧘 Relaxation Routine</h4>
                            <p>Develop a calming pre-sleep routine like reading or meditation.</p>
                        </div>
                        <div class="tip-card">
                            <h4>🌡️ Cool Environment</h4>
                            <p>Keep your bedroom cool (60-67°F) for optimal sleep conditions.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Sleep History -->
            <section class="sleep-section">
                <div class="dashboard-card">
                    <h2>📅 Sleep History</h2>
                    <div class="sleep-history" id="sleep-history">
                        <div class="no-entries">
                            <p>No sleep entries yet. Start logging your sleep to see your history!</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/openai-integration.js"></script>
    <script src="js/enhanced-features.js"></script>
    <script src="js/advanced-ai.js"></script>
    <script src="js/sleep-tracker.js"></script>

    <style>
        .sleep-content {
            max-width: 1200px;
            margin: 0 auto;
        }

        .sleep-section {
            margin-bottom: 2rem;
        }

        .sleep-entry-form {
            padding: 1.5rem 0;
        }

        .sleep-time-inputs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .quality-selector {
            display: flex;
            gap: 1rem;
            margin: 1rem 0;
            flex-wrap: wrap;
            justify-content: center;
        }

        .quality-option {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
            background: rgba(102, 126, 234, 0.05);
        }

        .quality-option:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .quality-option.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.15);
        }

        .quality-emoji {
            font-size: 2rem;
        }

        .factors-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }

        .factor-checkbox {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .factor-checkbox:hover {
            background: rgba(102, 126, 234, 0.1);
        }

        .factor-checkbox input:checked + span {
            font-weight: 600;
            color: #667eea;
        }

        .sleep-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
        }

        .stat-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: #666;
        }

        .chart-container {
            height: 300px;
            margin: 2rem 0;
            background: #f8f9fa;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .ai-sleep-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.3);
        }

        .recommendations-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .tip-card {
            padding: 1.5rem;
            background: rgba(86, 171, 47, 0.1);
            border: 2px solid rgba(86, 171, 47, 0.2);
            border-radius: 15px;
        }

        .tip-card h4 {
            margin: 0 0 1rem 0;
            color: #56ab2f;
        }

        .tip-card p {
            margin: 0;
            color: #333;
        }

        .sleep-entry {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            margin: 0.5rem 0;
            border-left: 4px solid #667eea;
        }

        .sleep-entry-info {
            flex: 1;
        }

        .sleep-entry-date {
            font-weight: 600;
            color: #333;
        }

        .sleep-entry-details {
            font-size: 0.9rem;
            color: #666;
            margin-top: 0.25rem;
        }

        .sleep-entry-quality {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .sleep-time-inputs {
                grid-template-columns: 1fr;
            }
            
            .quality-selector {
                flex-direction: column;
                align-items: center;
            }
            
            .factors-grid {
                grid-template-columns: 1fr;
            }
            
            .sleep-stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .recommendations-container {
                grid-template-columns: 1fr;
            }
        }
    </style>
</body>
</html>
