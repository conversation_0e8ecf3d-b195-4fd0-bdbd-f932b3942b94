<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to MindJourney - Onboarding</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="onboarding-page">
    <div class="onboarding-container">
        <!-- Progress Bar -->
        <div class="onboarding-progress">
            <div class="progress-bar">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            <div class="progress-text">
                <span id="current-step">1</span> of <span id="total-steps">6</span>
            </div>
        </div>

        <!-- Background Elements -->
        <div class="onboarding-background">
            <div class="floating-elements">
                <div class="floating-circle circle-1"></div>
                <div class="floating-circle circle-2"></div>
                <div class="floating-circle circle-3"></div>
            </div>
        </div>

        <!-- Step 1: Welcome -->
        <div class="onboarding-step active" id="step-1">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">🎉</div>
                    <h1>Welcome to MindJourney!</h1>
                    <p>Let's personalize your wellness experience in just a few steps.</p>
                </div>
                
                <div class="welcome-features">
                    <div class="feature-highlight">
                        <div class="feature-icon">🥽</div>
                        <h3>VR Meditation</h3>
                        <p>Immerse yourself in beautiful 3D environments for deeper relaxation</p>
                    </div>
                    <div class="feature-highlight">
                        <div class="feature-icon">🤖</div>
                        <h3>AI Companion</h3>
                        <p>Get personalized guidance and insights powered by advanced AI</p>
                    </div>
                    <div class="feature-highlight">
                        <div class="feature-icon">📊</div>
                        <h3>Progress Tracking</h3>
                        <p>Monitor your wellness journey with detailed analytics and insights</p>
                    </div>
                </div>

                <div class="step-actions">
                    <button class="onboarding-btn primary" onclick="nextStep()">Let's Get Started</button>
                </div>
            </div>
        </div>

        <!-- Step 2: Personal Info -->
        <div class="onboarding-step" id="step-2">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">👤</div>
                    <h2>Tell us about yourself</h2>
                    <p>Help us personalize your experience</p>
                </div>

                <form class="onboarding-form" id="personal-info-form">
                    <div class="form-group">
                        <label for="display-name">How would you like to be called?</label>
                        <input type="text" id="display-name" placeholder="Enter your preferred name" required>
                    </div>

                    <div class="form-group">
                        <label for="wellness-goals">What are your main wellness goals? (Select all that apply)</label>
                        <div class="checkbox-grid">
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="stress-relief">
                                <div class="card-content">
                                    <div class="card-icon">😌</div>
                                    <span>Stress Relief</span>
                                </div>
                            </label>
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="better-sleep">
                                <div class="card-content">
                                    <div class="card-icon">😴</div>
                                    <span>Better Sleep</span>
                                </div>
                            </label>
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="mindfulness">
                                <div class="card-content">
                                    <div class="card-icon">🧘</div>
                                    <span>Mindfulness</span>
                                </div>
                            </label>
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="emotional-balance">
                                <div class="card-content">
                                    <div class="card-icon">⚖️</div>
                                    <span>Emotional Balance</span>
                                </div>
                            </label>
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="focus">
                                <div class="card-content">
                                    <div class="card-icon">🎯</div>
                                    <span>Better Focus</span>
                                </div>
                            </label>
                            <label class="checkbox-card">
                                <input type="checkbox" name="goals" value="anxiety">
                                <div class="card-content">
                                    <div class="card-icon">🌊</div>
                                    <span>Anxiety Management</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="experience-level">How experienced are you with meditation?</label>
                        <div class="radio-cards">
                            <label class="radio-card">
                                <input type="radio" name="experience" value="beginner" required>
                                <div class="card-content">
                                    <div class="card-icon">🌱</div>
                                    <span>Beginner</span>
                                    <small>New to meditation</small>
                                </div>
                            </label>
                            <label class="radio-card">
                                <input type="radio" name="experience" value="intermediate" required>
                                <div class="card-content">
                                    <div class="card-icon">🌿</div>
                                    <span>Intermediate</span>
                                    <small>Some experience</small>
                                </div>
                            </label>
                            <label class="radio-card">
                                <input type="radio" name="experience" value="advanced" required>
                                <div class="card-content">
                                    <div class="card-icon">🌳</div>
                                    <span>Advanced</span>
                                    <small>Regular practitioner</small>
                                </div>
                            </label>
                        </div>
                    </div>
                </form>

                <div class="step-actions">
                    <button class="onboarding-btn secondary" onclick="previousStep()">Back</button>
                    <button class="onboarding-btn primary" onclick="nextStep()">Continue</button>
                </div>
            </div>
        </div>

        <!-- Step 3: VR Setup -->
        <div class="onboarding-step" id="step-3">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">🥽</div>
                    <h2>VR Experience Setup</h2>
                    <p>Let's check if you have VR capabilities</p>
                </div>

                <div class="vr-setup-content">
                    <div class="vr-status" id="vr-status">
                        <div class="status-icon">🔍</div>
                        <h3>Checking VR Support...</h3>
                        <p>Please wait while we detect your VR capabilities</p>
                    </div>

                    <div class="vr-options hidden" id="vr-options">
                        <div class="option-card">
                            <div class="option-icon">🥽</div>
                            <h4>VR Headset Detected!</h4>
                            <p>You can enjoy immersive 3D meditation experiences</p>
                            <button class="option-btn primary" id="enable-vr">Enable VR Features</button>
                        </div>
                        <div class="option-card">
                            <div class="option-icon">💻</div>
                            <h4>Desktop 3D Mode</h4>
                            <p>Experience 3D environments on your computer screen</p>
                            <button class="option-btn secondary" id="enable-desktop-3d">Use Desktop Mode</button>
                        </div>
                    </div>

                    <div class="vr-info">
                        <h4>Supported VR Headsets:</h4>
                        <ul>
                            <li>Meta Quest 2/3/Pro</li>
                            <li>HTC Vive</li>
                            <li>Valve Index</li>
                            <li>Pico 4</li>
                            <li>Any WebXR compatible headset</li>
                        </ul>
                    </div>
                </div>

                <div class="step-actions">
                    <button class="onboarding-btn secondary" onclick="previousStep()">Back</button>
                    <button class="onboarding-btn primary" onclick="nextStep()">Continue</button>
                </div>
            </div>
        </div>

        <!-- Step 4: AI Setup -->
        <div class="onboarding-step" id="step-4">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">🤖</div>
                    <h2>AI Companion Setup</h2>
                    <p>Unlock personalized guidance with AI features</p>
                </div>

                <div class="ai-setup-content">
                    <div class="ai-explanation">
                        <h3>Why AI?</h3>
                        <ul>
                            <li>Personalized meditation recommendations</li>
                            <li>Adaptive wellness insights</li>
                            <li>Intelligent progress tracking</li>
                            <li>Contextual emotional support</li>
                        </ul>
                    </div>

                    <div class="ai-setup-form">
                        <div class="form-group">
                            <label for="onboarding-api-key">OpenAI API Key (Optional)</label>
                            <input type="password" id="onboarding-api-key" placeholder="sk-proj-... (leave blank to skip)">
                            <small class="input-help">
                                Your API key enables advanced AI features. 
                                <a href="https://platform.openai.com/api-keys" target="_blank">Get your key here</a>
                            </small>
                        </div>

                        <div class="ai-privacy">
                            <h4>🔒 Privacy & Security</h4>
                            <ul>
                                <li>Your API key is stored locally on your device</li>
                                <li>No personal data is permanently stored by OpenAI</li>
                                <li>You can disable AI features at any time</li>
                                <li>All conversations are processed securely</li>
                            </ul>
                        </div>
                    </div>

                    <div class="ai-options">
                        <button class="option-btn primary" id="setup-ai">Setup AI Features</button>
                        <button class="option-btn secondary" id="skip-ai">Skip for Now</button>
                    </div>
                </div>

                <div class="step-actions">
                    <button class="onboarding-btn secondary" onclick="previousStep()">Back</button>
                    <button class="onboarding-btn primary" onclick="nextStep()">Continue</button>
                </div>
            </div>
        </div>

        <!-- Step 5: Preferences -->
        <div class="onboarding-step" id="step-5">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">⚙️</div>
                    <h2>Customize Your Experience</h2>
                    <p>Set your preferences for the best experience</p>
                </div>

                <form class="onboarding-form" id="preferences-form">
                    <div class="form-group">
                        <label>Notification Preferences</label>
                        <div class="toggle-options">
                            <label class="toggle-option">
                                <input type="checkbox" id="daily-reminders" checked>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Daily wellness reminders</span>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="streak-notifications" checked>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Streak milestone notifications</span>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="achievement-alerts" checked>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Achievement unlock alerts</span>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="preferred-time">Preferred meditation time</label>
                        <select id="preferred-time">
                            <option value="morning">Morning (6 AM - 12 PM)</option>
                            <option value="afternoon">Afternoon (12 PM - 6 PM)</option>
                            <option value="evening">Evening (6 PM - 10 PM)</option>
                            <option value="night">Night (10 PM - 6 AM)</option>
                            <option value="flexible">Flexible</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="session-duration">Default meditation duration</label>
                        <div class="duration-options">
                            <label class="duration-card">
                                <input type="radio" name="duration" value="300" required>
                                <div class="card-content">
                                    <span class="duration-time">5 min</span>
                                    <span class="duration-label">Quick</span>
                                </div>
                            </label>
                            <label class="duration-card">
                                <input type="radio" name="duration" value="600" checked required>
                                <div class="card-content">
                                    <span class="duration-time">10 min</span>
                                    <span class="duration-label">Standard</span>
                                </div>
                            </label>
                            <label class="duration-card">
                                <input type="radio" name="duration" value="1200" required>
                                <div class="card-content">
                                    <span class="duration-time">20 min</span>
                                    <span class="duration-label">Extended</span>
                                </div>
                            </label>
                            <label class="duration-card">
                                <input type="radio" name="duration" value="1800" required>
                                <div class="card-content">
                                    <span class="duration-time">30 min</span>
                                    <span class="duration-label">Deep</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Audio Preferences</label>
                        <div class="toggle-options">
                            <label class="toggle-option">
                                <input type="checkbox" id="ambient-sounds" checked>
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Ambient nature sounds</span>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="guided-meditations">
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Guided meditation voice</span>
                            </label>
                            <label class="toggle-option">
                                <input type="checkbox" id="binaural-beats">
                                <span class="toggle-slider"></span>
                                <span class="toggle-label">Binaural beats for focus</span>
                            </label>
                        </div>
                    </div>
                </form>

                <div class="step-actions">
                    <button class="onboarding-btn secondary" onclick="previousStep()">Back</button>
                    <button class="onboarding-btn primary" onclick="nextStep()">Continue</button>
                </div>
            </div>
        </div>

        <!-- Step 6: Complete -->
        <div class="onboarding-step" id="step-6">
            <div class="step-content">
                <div class="step-header">
                    <div class="step-icon">🎉</div>
                    <h2>You're All Set!</h2>
                    <p>Welcome to your personalized wellness journey</p>
                </div>

                <div class="completion-summary">
                    <div class="summary-card">
                        <h3>Your Setup Summary</h3>
                        <div class="summary-items" id="setup-summary">
                            <!-- Summary items will be populated by JavaScript -->
                        </div>
                    </div>

                    <div class="next-steps">
                        <h3>Recommended First Steps</h3>
                        <div class="recommendation-cards">
                            <div class="recommendation-card">
                                <div class="rec-icon">🧘</div>
                                <h4>Try VR Meditation</h4>
                                <p>Experience your first immersive meditation session</p>
                                <a href="vr-meditation.html" class="rec-link">Start Now</a>
                            </div>
                            <div class="recommendation-card">
                                <div class="rec-icon">📝</div>
                                <h4>Set Daily Goals</h4>
                                <p>Create personalized wellness goals for your journey</p>
                                <a href="goals.html" class="rec-link">Set Goals</a>
                            </div>
                            <div class="recommendation-card">
                                <div class="rec-icon">🤖</div>
                                <h4>Meet Your AI Companion</h4>
                                <p>Start a conversation with your wellness assistant</p>
                                <a href="bot.html" class="rec-link">Chat Now</a>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step-actions">
                    <button class="onboarding-btn primary large" onclick="completeOnboarding()">
                        Enter MindJourney
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/vr-controller.js"></script>
    <script>
        let currentStep = 1;
        const totalSteps = 6;
        let onboardingData = {};

        // Initialize onboarding
        document.addEventListener('DOMContentLoaded', function() {
            initializeOnboarding();
        });

        function initializeOnboarding() {
            // Check if user is signed in
            const currentUser = Auth.getCurrentUser();
            if (!currentUser) {
                window.location.href = 'signin.html';
                return;
            }

            // Pre-fill user data
            const displayNameInput = document.getElementById('display-name');
            if (displayNameInput && currentUser.firstName) {
                displayNameInput.value = currentUser.firstName;
            }

            // Setup step-specific initializations
            setupVRDetection();
            setupAIConfiguration();
            
            updateProgress();
        }

        function nextStep() {
            if (validateCurrentStep()) {
                saveCurrentStepData();
                
                if (currentStep < totalSteps) {
                    document.getElementById(`step-${currentStep}`).classList.remove('active');
                    currentStep++;
                    document.getElementById(`step-${currentStep}`).classList.add('active');
                    updateProgress();
                    
                    // Step-specific actions
                    if (currentStep === 3) {
                        checkVRSupport();
                    } else if (currentStep === 6) {
                        generateSummary();
                    }
                }
            }
        }

        function previousStep() {
            if (currentStep > 1) {
                document.getElementById(`step-${currentStep}`).classList.remove('active');
                currentStep--;
                document.getElementById(`step-${currentStep}`).classList.add('active');
                updateProgress();
            }
        }

        function updateProgress() {
            const progressFill = document.getElementById('progress-fill');
            const currentStepEl = document.getElementById('current-step');
            
            const progress = (currentStep / totalSteps) * 100;
            progressFill.style.width = progress + '%';
            currentStepEl.textContent = currentStep;
        }

        function validateCurrentStep() {
            switch (currentStep) {
                case 2:
                    const displayName = document.getElementById('display-name').value.trim();
                    const goals = document.querySelectorAll('input[name="goals"]:checked');
                    const experience = document.querySelector('input[name="experience"]:checked');
                    
                    if (!displayName) {
                        alert('Please enter your preferred name.');
                        return false;
                    }
                    if (goals.length === 0) {
                        alert('Please select at least one wellness goal.');
                        return false;
                    }
                    if (!experience) {
                        alert('Please select your meditation experience level.');
                        return false;
                    }
                    break;
                    
                case 5:
                    const duration = document.querySelector('input[name="duration"]:checked');
                    if (!duration) {
                        alert('Please select a default meditation duration.');
                        return false;
                    }
                    break;
            }
            return true;
        }

        function saveCurrentStepData() {
            switch (currentStep) {
                case 2:
                    onboardingData.displayName = document.getElementById('display-name').value.trim();
                    onboardingData.goals = Array.from(document.querySelectorAll('input[name="goals"]:checked')).map(cb => cb.value);
                    onboardingData.experience = document.querySelector('input[name="experience"]:checked').value;
                    break;
                    
                case 3:
                    // VR preferences saved in setupVRDetection
                    break;
                    
                case 4:
                    onboardingData.apiKey = document.getElementById('onboarding-api-key').value.trim();
                    break;
                    
                case 5:
                    onboardingData.notifications = {
                        dailyReminders: document.getElementById('daily-reminders').checked,
                        streakNotifications: document.getElementById('streak-notifications').checked,
                        achievementAlerts: document.getElementById('achievement-alerts').checked
                    };
                    onboardingData.preferredTime = document.getElementById('preferred-time').value;
                    onboardingData.defaultDuration = parseInt(document.querySelector('input[name="duration"]:checked').value);
                    onboardingData.audioPreferences = {
                        ambientSounds: document.getElementById('ambient-sounds').checked,
                        guidedMeditations: document.getElementById('guided-meditations').checked,
                        binauralBeats: document.getElementById('binaural-beats').checked
                    };
                    break;
            }
        }

        async function setupVRDetection() {
            // This will be called when step 3 is reached
        }

        async function checkVRSupport() {
            const statusEl = document.getElementById('vr-status');
            const optionsEl = document.getElementById('vr-options');
            
            try {
                const vrSupported = await VRController.checkVRSupport();
                
                if (vrSupported) {
                    statusEl.innerHTML = `
                        <div class="status-icon">✅</div>
                        <h3>VR Headset Detected!</h3>
                        <p>Your device supports immersive VR experiences</p>
                    `;
                    onboardingData.vrSupported = true;
                } else {
                    statusEl.innerHTML = `
                        <div class="status-icon">💻</div>
                        <h3>Desktop Mode Available</h3>
                        <p>VR headset not detected, but you can still enjoy 3D experiences</p>
                    `;
                    onboardingData.vrSupported = false;
                }
                
                optionsEl.classList.remove('hidden');
                
            } catch (error) {
                statusEl.innerHTML = `
                    <div class="status-icon">⚠️</div>
                    <h3>Unable to Check VR Support</h3>
                    <p>You can still use all features in desktop mode</p>
                `;
                onboardingData.vrSupported = false;
                optionsEl.classList.remove('hidden');
            }
        }

        function setupAIConfiguration() {
            document.getElementById('setup-ai').addEventListener('click', function() {
                const apiKey = document.getElementById('onboarding-api-key').value.trim();
                if (apiKey) {
                    // Save API key
                    if (window.API_CONFIG) {
                        API_CONFIG.OPENAI_API_KEY = apiKey;
                        localStorage.setItem('mindjourney_api_config', JSON.stringify(API_CONFIG));
                    }
                    onboardingData.aiEnabled = true;
                    alert('AI features enabled successfully!');
                } else {
                    alert('Please enter your OpenAI API key to enable AI features.');
                }
            });

            document.getElementById('skip-ai').addEventListener('click', function() {
                onboardingData.aiEnabled = false;
                alert('You can enable AI features later in settings.');
            });
        }

        function generateSummary() {
            const summaryEl = document.getElementById('setup-summary');
            let summaryHTML = '';
            
            if (onboardingData.displayName) {
                summaryHTML += `<div class="summary-item">👤 Name: ${onboardingData.displayName}</div>`;
            }
            
            if (onboardingData.goals && onboardingData.goals.length > 0) {
                summaryHTML += `<div class="summary-item">🎯 Goals: ${onboardingData.goals.join(', ')}</div>`;
            }
            
            if (onboardingData.experience) {
                summaryHTML += `<div class="summary-item">🧘 Experience: ${onboardingData.experience}</div>`;
            }
            
            if (onboardingData.vrSupported !== undefined) {
                summaryHTML += `<div class="summary-item">🥽 VR: ${onboardingData.vrSupported ? 'Enabled' : 'Desktop Mode'}</div>`;
            }
            
            if (onboardingData.aiEnabled !== undefined) {
                summaryHTML += `<div class="summary-item">🤖 AI: ${onboardingData.aiEnabled ? 'Enabled' : 'Disabled'}</div>`;
            }
            
            if (onboardingData.defaultDuration) {
                const minutes = onboardingData.defaultDuration / 60;
                summaryHTML += `<div class="summary-item">⏱️ Default Duration: ${minutes} minutes</div>`;
            }
            
            summaryEl.innerHTML = summaryHTML;
        }

        async function completeOnboarding() {
            try {
                // Save all onboarding data to user profile
                const userData = getUserData();
                if (userData) {
                    // Update user data with onboarding preferences
                    userData.onboardingCompleted = true;
                    userData.onboardingData = onboardingData;
                    userData.preferences = {
                        ...userData.preferences,
                        ...onboardingData.audioPreferences,
                        notifications: onboardingData.notifications,
                        preferredTime: onboardingData.preferredTime,
                        defaultDuration: onboardingData.defaultDuration
                    };
                    
                    // Update display name if provided
                    if (onboardingData.displayName) {
                        userData.username = onboardingData.displayName;
                    }
                    
                    saveUserData(userData);
                }
                
                // Update auth user profile
                if (onboardingData.displayName) {
                    await Auth.updateUserProfile({
                        displayName: onboardingData.displayName,
                        onboardingCompleted: true
                    });
                }
                
                // Show success message
                alert('Welcome to MindJourney! Your personalized wellness journey begins now.');
                
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
                
            } catch (error) {
                console.error('Error completing onboarding:', error);
                alert('There was an error saving your preferences. Please try again.');
            }
        }
    </script>
</body>
</html>
