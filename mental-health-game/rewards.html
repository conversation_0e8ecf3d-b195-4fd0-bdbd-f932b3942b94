<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rewards & Progress - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="rewards-page">
    <div class="container">
        <header class="page-header">
            <h1>Your Achievements</h1>
            <p>Celebrate your mental wellness journey and track your progress</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link active">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="rewards-content">
            <!-- Progress Overview -->
            <section class="progress-section">
                <div class="dashboard-card">
                    <h2>Your Journey Progress</h2>
                    <div class="user-avatar-display">
                        <div id="user-avatar" class="avatar-circle"></div>
                        <div class="user-info">
                            <h3 id="username">User</h3>
                            <p class="user-title">Mental Wellness Explorer</p>
                        </div>
                    </div>

                    <div class="stats-overview">
                        <div class="stat-card">
                            <div class="stat-icon">⭐</div>
                            <div class="stat-info">
                                <span class="stat-number" id="level-display">1</span>
                                <span class="stat-label">Level</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-info">
                                <span class="stat-number" id="xp-display">0</span>
                                <span class="stat-label">Total XP</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🔥</div>
                            <div class="stat-info">
                                <span class="stat-number" id="streak-display">0</span>
                                <span class="stat-label">Day Streak</span>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">🏆</div>
                            <div class="stat-info">
                                <span class="stat-number" id="badges-display">0</span>
                                <span class="stat-label">Badges</span>
                            </div>
                        </div>
                    </div>

                    <div class="level-progress-section">
                        <h3>Level Progress</h3>
                        <div class="progress-container">
                            <div id="level-progress-bar" class="progress-bar" style="width: 0%">
                                <span class="progress-text">0%</span>
                            </div>
                        </div>
                        <p id="level-progress-text">0 / 50 XP to next level</p>
                    </div>
                </div>
            </section>

            <!-- Badges Section -->
            <section class="badges-section">
                <div class="dashboard-card">
                    <h2>Badge Collection</h2>
                    <p>Unlock badges by completing tasks and reaching milestones</p>
                    
                    <div class="badge-categories">
                        <button class="category-btn active" data-category="all">All Badges</button>
                        <button class="category-btn" data-category="earned">Earned</button>
                        <button class="category-btn" data-category="locked">Locked</button>
                    </div>

                    <div id="badges-grid" class="badges-grid">
                        <!-- Badges will be populated by JavaScript -->
                    </div>
                </div>
            </section>

            <!-- Statistics Section -->
            <section class="statistics-section">
                <div class="dashboard-card">
                    <h2>Wellness Statistics</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <span class="stat-label">Total Days Active</span>
                            <span id="total-days" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Breathing Sessions</span>
                            <span id="breathing-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Journal Entries</span>
                            <span id="journal-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Mood Entries</span>
                            <span id="mood-count" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Meditation Minutes</span>
                            <span id="meditation-minutes" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Best Streak</span>
                            <span id="best-streak" class="stat-value">0</span>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Recent Activity -->
            <section class="activity-section">
                <div class="dashboard-card">
                    <h2>Recent Activity</h2>
                    <div id="recent-activity" class="activity-list">
                        <p class="no-activity">Start completing tasks to see your activity here!</p>
                    </div>
                </div>
            </section>

            <!-- Motivational Section -->
            <section class="motivation-section">
                <div class="dashboard-card motivation-card">
                    <h2>Keep Going!</h2>
                    <div class="motivation-content">
                        <div class="motivation-icon">🌟</div>
                        <div class="motivation-text">
                            <p id="motivation-message">Every step you take on your mental wellness journey matters. You're building resilience, one day at a time.</p>
                            <p class="motivation-tip" id="next-goal">Complete your daily tasks to earn your next badge!</p>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadRewardsPage();
            setupBadgeCategories();
        });

        function loadRewardsPage() {
            const userData = getUserData();
            if (!userData) {
                window.location.href = 'index.html';
                return;
            }

            updateUserInfo(userData);
            updateStats(userData);
            updateLevelProgress(userData);
            displayBadges(userData);
            displayRecentActivity(userData);
            updateMotivation(userData);
        }

        function updateUserInfo(userData) {
            document.getElementById('username').textContent = userData.username;
            
            const avatarElement = document.getElementById('user-avatar');
            avatarElement.className = `avatar-circle ${userData.avatar}-avatar`;
            avatarElement.textContent = getAvatarEmoji(userData.avatar);
        }

        function updateStats(userData) {
            document.getElementById('level-display').textContent = userData.level;
            document.getElementById('xp-display').textContent = userData.xp;
            document.getElementById('streak-display').textContent = userData.streak;
            document.getElementById('badges-display').textContent = userData.badges.length;
            
            document.getElementById('total-days').textContent = userData.totalDays;
            document.getElementById('breathing-count').textContent = userData.stats.totalBreathingSessions;
            document.getElementById('journal-count').textContent = userData.stats.totalJournalEntries;
            document.getElementById('mood-count').textContent = userData.stats.totalMoodEntries;
            document.getElementById('meditation-minutes').textContent = userData.stats.totalMeditationMinutes;
            document.getElementById('best-streak').textContent = Math.max(userData.streak, 0); // Could track historical best
        }

        function updateLevelProgress(userData) {
            const progress = getProgressToNextLevel();
            if (progress) {
                const progressBar = document.getElementById('level-progress-bar');
                const progressText = document.getElementById('level-progress-text');
                
                if (progress.isMaxLevel) {
                    progressBar.style.width = '100%';
                    progressBar.querySelector('.progress-text').textContent = 'MAX';
                    progressText.textContent = 'Maximum level reached! You are a Mental Wellness Master!';
                } else {
                    progressBar.style.width = progress.progress + '%';
                    progressBar.querySelector('.progress-text').textContent = progress.progress + '%';
                    progressText.textContent = `${progress.currentXP} / ${progress.requiredXP} XP to Level ${progress.nextLevel}`;
                }
            }
        }

        function displayBadges(userData) {
            const badgesGrid = document.getElementById('badges-grid');
            const earnedBadges = userData.badges;
            
            let badgesHTML = '';
            
            Object.keys(badgeDefinitions).forEach(badgeId => {
                const badge = badgeDefinitions[badgeId];
                const isEarned = earnedBadges.includes(badgeId);
                
                badgesHTML += `
                    <div class="badge-card ${isEarned ? 'earned' : 'locked'}" data-category="${isEarned ? 'earned' : 'locked'}">
                        <div class="badge-icon ${isEarned ? '' : 'grayscale'}">${badge.icon}</div>
                        <div class="badge-info">
                            <h4 class="badge-name">${badge.name}</h4>
                            <p class="badge-description">${badge.description}</p>
                            ${isEarned ? '<span class="badge-status earned">Earned!</span>' : '<span class="badge-status locked">Locked</span>'}
                        </div>
                    </div>
                `;
            });
            
            badgesGrid.innerHTML = badgesHTML;
        }

        function setupBadgeCategories() {
            document.querySelectorAll('.category-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const category = this.dataset.category;
                    
                    // Update active button
                    document.querySelectorAll('.category-btn').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    
                    // Filter badges
                    filterBadges(category);
                });
            });
        }

        function filterBadges(category) {
            const badges = document.querySelectorAll('.badge-card');
            
            badges.forEach(badge => {
                if (category === 'all') {
                    badge.style.display = 'flex';
                } else {
                    const badgeCategory = badge.dataset.category;
                    badge.style.display = badgeCategory === category ? 'flex' : 'none';
                }
            });
        }

        function displayRecentActivity(userData) {
            const activityContainer = document.getElementById('recent-activity');
            const activities = [];
            
            // Add recent journal entries
            if (userData.journalEntries && userData.journalEntries.length > 0) {
                userData.journalEntries.slice(-3).forEach(entry => {
                    activities.push({
                        type: 'journal',
                        date: entry.date,
                        text: 'Wrote a journal entry'
                    });
                });
            }
            
            // Add recent mood entries
            if (userData.moodHistory && userData.moodHistory.length > 0) {
                userData.moodHistory.slice(-3).forEach(mood => {
                    activities.push({
                        type: 'mood',
                        date: mood.date,
                        text: `Tracked mood: ${getMoodLabel(mood.mood)}`
                    });
                });
            }
            
            // Add task completions for today
            const today = getCurrentDate();
            Object.keys(userData.dailyTasks).forEach(taskType => {
                const task = userData.dailyTasks[taskType];
                if (task.completed && task.date === today) {
                    activities.push({
                        type: taskType,
                        date: today,
                        text: `Completed ${taskType} exercise`
                    });
                }
            });
            
            if (activities.length === 0) {
                activityContainer.innerHTML = '<p class="no-activity">Start completing tasks to see your activity here!</p>';
                return;
            }
            
            // Sort by date (most recent first)
            activities.sort((a, b) => new Date(b.date) - new Date(a.date));
            
            const activityHTML = activities.slice(0, 5).map(activity => `
                <div class="activity-item">
                    <div class="activity-icon">${getActivityIcon(activity.type)}</div>
                    <div class="activity-details">
                        <span class="activity-text">${activity.text}</span>
                        <span class="activity-date">${formatDate(activity.date)}</span>
                    </div>
                </div>
            `).join('');
            
            activityContainer.innerHTML = activityHTML;
        }

        function getActivityIcon(type) {
            const icons = {
                breathing: '🌬️',
                journal: '📝',
                mood: '📊',
                meditation: '🧘'
            };
            return icons[type] || '✅';
        }

        function updateMotivation(userData) {
            const motivationMessages = [
                "Every step you take on your mental wellness journey matters. You're building resilience, one day at a time.",
                "Your commitment to self-care is inspiring. Keep nurturing your mental health!",
                "Progress isn't always linear, but you're moving forward. That's what counts.",
                "You're creating positive habits that will benefit you for life. Well done!",
                "Mental wellness is a journey, not a destination. You're on the right path."
            ];
            
            const nextGoals = [];
            
            // Suggest next goals based on progress
            if (userData.stats.totalBreathingSessions === 0) {
                nextGoals.push("Try your first breathing exercise!");
            } else if (userData.stats.totalBreathingSessions < 5) {
                nextGoals.push("Complete 5 breathing exercises to build the habit!");
            }
            
            if (userData.stats.totalJournalEntries === 0) {
                nextGoals.push("Write your first journal entry!");
            } else if (userData.stats.totalJournalEntries < 10) {
                nextGoals.push("Write 10 journal entries to unlock the Mindful Writer badge!");
            }
            
            if (userData.streak === 0) {
                nextGoals.push("Start a new streak by completing today's tasks!");
            } else if (userData.streak < 3) {
                nextGoals.push("Reach a 3-day streak to unlock the Consistent Soul badge!");
            }
            
            const randomMessage = motivationMessages[Math.floor(Math.random() * motivationMessages.length)];
            const nextGoal = nextGoals.length > 0 ? nextGoals[0] : "Keep up the amazing work!";
            
            document.getElementById('motivation-message').textContent = randomMessage;
            document.getElementById('next-goal').textContent = nextGoal;
        }

        function getAvatarEmoji(avatarType) {
            const avatars = {
                peaceful: '🧘',
                nature: '🌱',
                strong: '💪',
                wise: '🦉',
                creative: '🎨',
                bright: '⭐'
            };
            return avatars[avatarType] || '🧘';
        }
    </script>

    <style>
        .user-avatar-display {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            justify-content: center;
        }

        .user-info h3 {
            margin: 0;
            color: #333;
            font-size: 1.5rem;
        }

        .user-title {
            color: #667eea;
            font-weight: 600;
            margin: 0;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .stat-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 15px;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
        }

        .stat-icon {
            font-size: 2rem;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: 700;
            color: #667eea;
        }

        .stat-label {
            display: block;
            font-size: 0.9rem;
            color: #666;
        }

        .badge-categories {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .category-btn {
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .category-btn:hover, .category-btn.active {
            background: #667eea;
            color: white;
        }

        .badges-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .badge-card {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            border-radius: 15px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .badge-card.earned {
            background: rgba(86, 171, 47, 0.1);
            border-color: rgba(86, 171, 47, 0.3);
        }

        .badge-card.locked {
            background: rgba(0, 0, 0, 0.05);
            border-color: rgba(0, 0, 0, 0.1);
        }

        .badge-card .badge-icon {
            font-size: 2.5rem;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
        }

        .badge-card .badge-icon.grayscale {
            filter: grayscale(100%);
            opacity: 0.5;
        }

        .badge-name {
            margin: 0 0 0.5rem 0;
            color: #333;
            font-size: 1.1rem;
        }

        .badge-description {
            margin: 0 0 0.5rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .badge-status {
            font-size: 0.8rem;
            font-weight: 600;
            padding: 2px 8px;
            border-radius: 10px;
        }

        .badge-status.earned {
            background: #56ab2f;
            color: white;
        }

        .badge-status.locked {
            background: #ccc;
            color: #666;
        }

        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .activity-item {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
        }

        .activity-icon {
            font-size: 1.5rem;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(102, 126, 234, 0.1);
            border-radius: 50%;
        }

        .activity-details {
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
        }

        .activity-text {
            font-weight: 600;
            color: #333;
        }

        .activity-date {
            font-size: 0.8rem;
            color: #666;
        }

        .motivation-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border: 2px solid rgba(102, 126, 234, 0.2);
        }

        .motivation-content {
            display: flex;
            align-items: center;
            gap: 1.5rem;
        }

        .motivation-icon {
            font-size: 3rem;
            animation: pulse 2s infinite;
        }

        .motivation-text p {
            margin: 0 0 1rem 0;
        }

        .motivation-tip {
            font-weight: 600;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .stats-overview {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .badges-grid {
                grid-template-columns: 1fr;
            }
            
            .motivation-content {
                flex-direction: column;
                text-align: center;
            }
        }
    </style>
    <script src="js/enhanced-features.js"></script>
</body>
</html>
