<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth-styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="auth-page">
    <div class="auth-container">
        <!-- Background Elements -->
        <div class="auth-background">
            <div class="floating-elements">
                <div class="floating-circle circle-1"></div>
                <div class="floating-circle circle-2"></div>
                <div class="floating-circle circle-3"></div>
                <div class="floating-circle circle-4"></div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="auth-content">
            <!-- Header -->
            <header class="auth-header">
                <div class="app-logo">
                    <h1 class="app-title">🧘 MindJourney</h1>
                    <p class="app-subtitle">Your Personal Mental Wellness Adventure</p>
                </div>
            </header>

            <!-- Sign In Form -->
            <main class="auth-main">
                <div class="auth-card">
                    <div class="auth-card-header">
                        <h2>Welcome Back!</h2>
                        <p>Continue your wellness journey</p>
                    </div>

                    <form id="signin-form" class="auth-form">
                        <div class="form-group">
                            <label for="signin-email">Email Address</label>
                            <input 
                                type="email" 
                                id="signin-email" 
                                name="email" 
                                placeholder="Enter your email"
                                required
                                autocomplete="email"
                            >
                            <div class="input-icon">📧</div>
                        </div>

                        <div class="form-group">
                            <label for="signin-password">Password</label>
                            <input 
                                type="password" 
                                id="signin-password" 
                                name="password" 
                                placeholder="Enter your password"
                                required
                                autocomplete="current-password"
                            >
                            <div class="input-icon">🔒</div>
                            <button type="button" class="password-toggle" id="password-toggle">
                                <span class="toggle-icon">👁️</span>
                            </button>
                        </div>

                        <div class="form-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="remember-me" name="remember">
                                <span class="checkmark"></span>
                                Remember me
                            </label>
                            <a href="#" class="forgot-password" id="forgot-password-link">Forgot password?</a>
                        </div>

                        <button type="submit" class="auth-btn primary" id="signin-btn">
                            <span class="btn-text">Sign In</span>
                            <span class="btn-loader hidden">
                                <div class="spinner"></div>
                            </span>
                        </button>

                        <div class="auth-divider">
                            <span>or</span>
                        </div>

                        <div class="social-signin">
                            <button type="button" class="social-btn google-btn" id="google-signin">
                                <span class="social-icon">🔍</span>
                                Continue with Google
                            </button>
                            <button type="button" class="social-btn apple-btn" id="apple-signin">
                                <span class="social-icon">🍎</span>
                                Continue with Apple
                            </button>
                        </div>
                    </form>

                    <div class="auth-footer">
                        <p>Don't have an account? <a href="signup.html" class="auth-link">Sign up here</a></p>
                    </div>
                </div>

                <!-- Quick Access -->
                <div class="quick-access">
                    <h3>Quick Access</h3>
                    <div class="quick-access-grid">
                        <a href="vr-meditation.html" class="quick-access-item">
                            <div class="quick-icon">🥽</div>
                            <span>VR Meditation</span>
                        </a>
                        <a href="tasks.html" class="quick-access-item">
                            <div class="quick-icon">🎯</div>
                            <span>Daily Tasks</span>
                        </a>
                        <a href="bot.html" class="quick-access-item">
                            <div class="quick-icon">🤖</div>
                            <span>AI Companion</span>
                        </a>
                        <a href="setup.html" class="quick-access-item">
                            <div class="quick-icon">⚙️</div>
                            <span>Setup Guide</span>
                        </a>
                    </div>
                </div>
            </main>
        </div>

        <!-- Error/Success Messages -->
        <div id="auth-message" class="auth-message hidden">
            <div class="message-content">
                <span class="message-icon"></span>
                <span class="message-text"></span>
                <button class="message-close">&times;</button>
            </div>
        </div>

        <!-- Forgot Password Modal -->
        <div id="forgot-password-modal" class="modal hidden">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>Reset Password</h3>
                    <button class="modal-close" id="close-forgot-modal">&times;</button>
                </div>
                <div class="modal-body">
                    <p>Enter your email address and we'll send you a link to reset your password.</p>
                    <form id="forgot-password-form">
                        <div class="form-group">
                            <label for="reset-email">Email Address</label>
                            <input 
                                type="email" 
                                id="reset-email" 
                                placeholder="Enter your email"
                                required
                            >
                        </div>
                        <button type="submit" class="auth-btn primary">Send Reset Link</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Demo Mode Banner -->
        <div class="demo-banner">
            <div class="demo-content">
                <span class="demo-icon">🚀</span>
                <span class="demo-text">Demo Mode: Try without signing up</span>
                <a href="dashboard.html" class="demo-link">Continue as Guest</a>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/auth.js"></script>
    <script>
        // Initialize sign in page
        document.addEventListener('DOMContentLoaded', function() {
            initializeSignIn();
        });

        function initializeSignIn() {
            // Check if user is already signed in
            const currentUser = Auth.getCurrentUser();
            if (currentUser) {
                window.location.href = 'dashboard.html';
                return;
            }

            // Setup form handlers
            setupSignInForm();
            setupPasswordToggle();
            setupForgotPassword();
            setupSocialSignIn();
            setupDemoMode();
        }

        function setupSignInForm() {
            const form = document.getElementById('signin-form');
            const submitBtn = document.getElementById('signin-btn');

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const email = document.getElementById('signin-email').value;
                const password = document.getElementById('signin-password').value;
                const remember = document.getElementById('remember-me').checked;

                // Show loading state
                showButtonLoading(submitBtn, true);

                try {
                    const result = await Auth.signIn(email, password, remember);
                    
                    if (result.success) {
                        showMessage('success', 'Welcome back! Redirecting to your dashboard...');
                        
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 1500);
                    } else {
                        showMessage('error', result.message || 'Sign in failed. Please try again.');
                    }
                } catch (error) {
                    showMessage('error', 'An error occurred. Please try again.');
                    console.error('Sign in error:', error);
                } finally {
                    showButtonLoading(submitBtn, false);
                }
            });
        }

        function setupPasswordToggle() {
            const toggle = document.getElementById('password-toggle');
            const passwordInput = document.getElementById('signin-password');

            toggle.addEventListener('click', function() {
                const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordInput.setAttribute('type', type);
                
                const icon = toggle.querySelector('.toggle-icon');
                icon.textContent = type === 'password' ? '👁️' : '🙈';
            });
        }

        function setupForgotPassword() {
            const forgotLink = document.getElementById('forgot-password-link');
            const modal = document.getElementById('forgot-password-modal');
            const closeBtn = document.getElementById('close-forgot-modal');
            const form = document.getElementById('forgot-password-form');

            forgotLink.addEventListener('click', function(e) {
                e.preventDefault();
                modal.classList.remove('hidden');
            });

            closeBtn.addEventListener('click', function() {
                modal.classList.add('hidden');
            });

            form.addEventListener('submit', async function(e) {
                e.preventDefault();
                const email = document.getElementById('reset-email').value;
                
                try {
                    await Auth.requestPasswordReset(email);
                    showMessage('success', 'Password reset link sent to your email!');
                    modal.classList.add('hidden');
                } catch (error) {
                    showMessage('error', 'Failed to send reset link. Please try again.');
                }
            });
        }

        function setupSocialSignIn() {
            document.getElementById('google-signin').addEventListener('click', function() {
                showMessage('info', 'Google Sign-In coming soon!');
            });

            document.getElementById('apple-signin').addEventListener('click', function() {
                showMessage('info', 'Apple Sign-In coming soon!');
            });
        }

        function setupDemoMode() {
            const demoLink = document.querySelector('.demo-link');
            demoLink.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Create demo user
                Auth.createDemoUser();
                showMessage('success', 'Demo mode activated! Redirecting...');
                
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            });
        }

        function showButtonLoading(button, loading) {
            const text = button.querySelector('.btn-text');
            const loader = button.querySelector('.btn-loader');
            
            if (loading) {
                text.classList.add('hidden');
                loader.classList.remove('hidden');
                button.disabled = true;
            } else {
                text.classList.remove('hidden');
                loader.classList.add('hidden');
                button.disabled = false;
            }
        }

        function showMessage(type, message) {
            const messageEl = document.getElementById('auth-message');
            const icon = messageEl.querySelector('.message-icon');
            const text = messageEl.querySelector('.message-text');
            const close = messageEl.querySelector('.message-close');

            // Set message content
            text.textContent = message;
            
            // Set icon based on type
            const icons = {
                success: '✅',
                error: '❌',
                info: 'ℹ️',
                warning: '⚠️'
            };
            icon.textContent = icons[type] || 'ℹ️';

            // Set message class
            messageEl.className = `auth-message ${type}`;
            messageEl.classList.remove('hidden');

            // Auto hide after 5 seconds
            setTimeout(() => {
                messageEl.classList.add('hidden');
            }, 5000);

            // Close button handler
            close.onclick = () => messageEl.classList.add('hidden');
        }
    </script>
</body>
</html>
