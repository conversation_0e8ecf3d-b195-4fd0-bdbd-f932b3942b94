<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VR Meditation Space - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/vr-styles.css">
    <link rel="stylesheet" href="css/animations.css">
    
    <!-- Three.js for 3D graphics -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <!-- WebXR Polyfill for VR support -->
    <script src="https://cdn.jsdelivr.net/npm/webxr-polyfill@latest/build/webxr-polyfill.js"></script>
    <!-- VR Button for Three.js -->
    <script src="https://threejs.org/examples/js/webxr/VRButton.js"></script>
</head>
<body class="vr-page">
    <div class="container">
        <!-- VR Headset Notification -->
        <div id="vr-notification" class="vr-notification hidden">
            <div class="vr-notification-content">
                <div class="vr-icon">🥽</div>
                <h3>Enhanced VR Experience Available!</h3>
                <p>Insert your VR headset for an immersive 3D meditation experience</p>
                <div class="vr-notification-buttons">
                    <button id="enter-vr-btn" class="primary-btn">Enter VR Mode</button>
                    <button id="continue-desktop-btn" class="secondary-btn">Continue on Desktop</button>
                </div>
            </div>
        </div>

        <header class="app-header">
            <div class="header-content">
                <h1 class="app-title">🧘 VR Meditation Space</h1>
                <nav class="main-nav">
                    <a href="dashboard.html" class="nav-link">← Back to Dashboard</a>
                    <a href="tasks.html" class="nav-link">Tasks</a>
                </nav>
            </div>
        </header>

        <!-- 3D Scene Container -->
        <div id="vr-container" class="vr-container">
            <canvas id="vr-canvas"></canvas>
            
            <!-- VR UI Overlay -->
            <div id="vr-ui" class="vr-ui">
                <div class="vr-controls">
                    <div class="environment-selector">
                        <h3>Choose Your Environment</h3>
                        <div class="environment-options">
                            <button class="env-btn active" data-env="forest">🌲 Forest</button>
                            <button class="env-btn" data-env="beach">🏖️ Beach</button>
                            <button class="env-btn" data-env="mountain">🏔️ Mountain</button>
                            <button class="env-btn" data-env="space">🌌 Space</button>
                        </div>
                    </div>

                    <div class="meditation-controls">
                        <h3>Meditation Settings</h3>
                        <div class="duration-selector">
                            <label>Duration:</label>
                            <select id="meditation-duration">
                                <option value="300">5 minutes</option>
                                <option value="600">10 minutes</option>
                                <option value="900">15 minutes</option>
                                <option value="1200">20 minutes</option>
                                <option value="1800">30 minutes</option>
                            </select>
                        </div>
                        
                        <div class="audio-controls">
                            <label>
                                <input type="checkbox" id="ambient-sounds" checked>
                                Ambient Sounds
                            </label>
                            <label>
                                <input type="checkbox" id="guided-voice">
                                Guided Voice
                            </label>
                            <label>
                                <input type="checkbox" id="binaural-beats">
                                Binaural Beats
                            </label>
                        </div>
                    </div>

                    <div class="session-controls">
                        <button id="start-vr-meditation" class="primary-btn large">
                            🧘 Start VR Meditation
                        </button>
                        <button id="stop-vr-meditation" class="secondary-btn hidden">
                            Stop Session
                        </button>
                    </div>
                </div>

                <!-- Meditation Timer Display -->
                <div id="vr-timer" class="vr-timer hidden">
                    <div class="timer-circle">
                        <span id="vr-time-display">5:00</span>
                    </div>
                    <div class="meditation-phase" id="meditation-phase">
                        Preparing your space...
                    </div>
                </div>

                <!-- Breathing Guide -->
                <div id="vr-breathing-guide" class="vr-breathing-guide hidden">
                    <div class="breathing-orb" id="breathing-orb"></div>
                    <div class="breathing-text" id="breathing-text">Breathe naturally</div>
                </div>
            </div>
        </div>

        <!-- Desktop Fallback -->
        <div id="desktop-fallback" class="desktop-fallback hidden">
            <div class="fallback-content">
                <h2>3D Meditation Experience</h2>
                <p>Experience immersive meditation in a beautiful 3D environment. Use your mouse to look around and WASD keys to move.</p>
                
                <div class="fallback-instructions">
                    <h3>Controls:</h3>
                    <ul>
                        <li><strong>Mouse:</strong> Look around</li>
                        <li><strong>WASD:</strong> Move around</li>
                        <li><strong>Space:</strong> Pause/Resume</li>
                        <li><strong>Esc:</strong> Exit meditation</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Loading Screen -->
        <div id="vr-loading" class="vr-loading">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <h3>Preparing Your Meditation Space...</h3>
                <p id="loading-status">Initializing 3D environment...</p>
            </div>
        </div>

        <!-- Session Complete -->
        <div id="session-complete" class="session-complete hidden">
            <div class="complete-content">
                <div class="complete-icon">✨</div>
                <h2>Meditation Complete!</h2>
                <p>You've successfully completed your VR meditation session.</p>
                <div class="session-stats">
                    <div class="stat">
                        <span class="stat-value" id="session-duration">5:00</span>
                        <span class="stat-label">Duration</span>
                    </div>
                    <div class="stat">
                        <span class="stat-value">+30 XP</span>
                        <span class="stat-label">Experience</span>
                    </div>
                </div>
                <button id="return-dashboard" class="primary-btn">Return to Dashboard</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/vr-controller.js"></script>
    <script src="js/three-scene.js"></script>
    
    <script>
        // Initialize VR Meditation when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeVRMeditation();
        });

        async function initializeVRMeditation() {
            try {
                // Check for VR support
                const vrSupported = await VRController.checkVRSupport();
                
                if (vrSupported) {
                    document.getElementById('vr-notification').classList.remove('hidden');
                } else {
                    // Show desktop fallback
                    document.getElementById('desktop-fallback').classList.remove('hidden');
                }

                // Initialize 3D scene
                await ThreeScene.initialize();
                
                // Setup event listeners
                setupVREventListeners();
                
            } catch (error) {
                console.error('Failed to initialize VR meditation:', error);
                showDesktopFallback();
            }
        }

        function setupVREventListeners() {
            // VR notification buttons
            document.getElementById('enter-vr-btn').addEventListener('click', enterVRMode);
            document.getElementById('continue-desktop-btn').addEventListener('click', continueDesktopMode);
            
            // Environment selection
            document.querySelectorAll('.env-btn').forEach(btn => {
                btn.addEventListener('click', (e) => selectEnvironment(e.target.dataset.env));
            });
            
            // Meditation controls
            document.getElementById('start-vr-meditation').addEventListener('click', startVRMeditation);
            document.getElementById('stop-vr-meditation').addEventListener('click', stopVRMeditation);
            document.getElementById('return-dashboard').addEventListener('click', () => {
                window.location.href = 'dashboard.html';
            });
        }

        async function enterVRMode() {
            try {
                await VRController.enterVR();
                document.getElementById('vr-notification').classList.add('hidden');
            } catch (error) {
                console.error('Failed to enter VR mode:', error);
                continueDesktopMode();
            }
        }

        function continueDesktopMode() {
            document.getElementById('vr-notification').classList.add('hidden');
            document.getElementById('desktop-fallback').classList.remove('hidden');
        }

        function selectEnvironment(environment) {
            // Update active button
            document.querySelectorAll('.env-btn').forEach(btn => btn.classList.remove('active'));
            document.querySelector(`[data-env="${environment}"]`).classList.add('active');
            
            // Change 3D environment
            ThreeScene.changeEnvironment(environment);
        }

        async function startVRMeditation() {
            const duration = parseInt(document.getElementById('meditation-duration').value);
            const settings = {
                ambientSounds: document.getElementById('ambient-sounds').checked,
                guidedVoice: document.getElementById('guided-voice').checked,
                binauralBeats: document.getElementById('binaural-beats').checked
            };

            // Hide controls and show timer
            document.getElementById('vr-ui').classList.add('meditation-mode');
            document.getElementById('vr-timer').classList.remove('hidden');
            document.getElementById('vr-breathing-guide').classList.remove('hidden');
            
            // Start meditation session
            await VRController.startMeditationSession(duration, settings);
        }

        function stopVRMeditation() {
            VRController.stopMeditationSession();
            showSessionComplete();
        }

        function showSessionComplete() {
            document.getElementById('session-complete').classList.remove('hidden');
            document.getElementById('vr-timer').classList.add('hidden');
            document.getElementById('vr-breathing-guide').classList.add('hidden');
            
            // Complete task and add XP
            completeTaskWithRewards('vrMeditation');
        }

        function showDesktopFallback() {
            document.getElementById('vr-notification').classList.add('hidden');
            document.getElementById('desktop-fallback').classList.remove('hidden');
        }
    </script>
</body>
</html>
