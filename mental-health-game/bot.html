<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Companion - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="bot-page">
    <div class="container">
        <header class="page-header">
            <h1>Your AI Wellness Companion</h1>
            <p>Chat with your supportive AI friend for motivation and guidance</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link active">AI Companion</a>
            </nav>
        </header>

        <main class="bot-content">
            <div class="chat-container">
                <div class="bot-avatar-section">
                    <div class="bot-avatar">🤖</div>
                    <div class="bot-info">
                        <h3>Mindful Assistant</h3>
                        <p class="bot-status" id="bot-status">Ready to chat</p>
                    </div>
                </div>

                <div class="chat-messages" id="chat-messages">
                    <div class="message bot-message">
                        <div class="message-avatar">🤖</div>
                        <div class="message-content">
                            <p>Hello! I'm your AI wellness companion. I'm here to support you on your mental health journey with encouragement, tips, and a listening ear. How are you feeling today?</p>
                        </div>
                    </div>
                </div>

                <div class="chat-input-section">
                    <div class="quick-responses">
                        <button class="quick-response-btn" data-response="How are you?">How are you?</button>
                        <button class="quick-response-btn" data-response="I need motivation">I need motivation</button>
                        <button class="quick-response-btn" data-response="I'm feeling stressed">I'm feeling stressed</button>
                        <button class="quick-response-btn" data-response="Give me a tip">Give me a tip</button>
                    </div>
                    
                    <div class="chat-input-container">
                        <input type="text" id="chat-input" placeholder="Type your message here..." maxlength="200">
                        <button id="send-btn" class="send-btn">Send</button>
                    </div>
                </div>
            </div>

            <div class="bot-features">
                <div class="feature-card">
                    <h3>🌟 Daily Motivation</h3>
                    <p>Get personalized encouragement and motivational quotes</p>
                    <button class="feature-btn" data-action="motivation">Get Motivated</button>
                </div>
                
                <div class="feature-card">
                    <h3>💡 Wellness Tips</h3>
                    <p>Receive practical mental health tips and strategies</p>
                    <button class="feature-btn" data-action="tips">Get Tips</button>
                </div>
                
                <div class="feature-card">
                    <h3>🧘 Guided Breathing</h3>
                    <p>Let me guide you through a calming breathing exercise</p>
                    <button class="feature-btn" data-action="breathing">Start Breathing</button>
                </div>
                
                <div class="feature-card">
                    <h3>📊 Progress Check</h3>
                    <p>Review your wellness journey and celebrate achievements</p>
                    <button class="feature-btn" data-action="progress">Check Progress</button>
                </div>
            </div>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script>
        let isTyping = false;

        document.addEventListener('DOMContentLoaded', function() {
            setupChatInterface();
            setupFeatureButtons();
            
            // Add welcome message based on user data
            const userData = getUserData();
            if (userData) {
                setTimeout(() => {
                    addBotMessage(`Nice to see you again, ${userData.username}! I see you're on level ${userData.level} with ${userData.xp} XP. How can I support you today?`);
                }, 1000);
            }
        });

        function setupChatInterface() {
            const chatInput = document.getElementById('chat-input');
            const sendBtn = document.getElementById('send-btn');

            // Send message on Enter key
            chatInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Send button click
            sendBtn.addEventListener('click', sendMessage);

            // Quick response buttons
            document.querySelectorAll('.quick-response-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const response = this.dataset.response;
                    addUserMessage(response);
                    handleUserMessage(response);
                });
            });

            // Enable/disable send button based on input
            chatInput.addEventListener('input', function() {
                sendBtn.disabled = this.value.trim().length === 0;
            });
        }

        function setupFeatureButtons() {
            document.querySelectorAll('.feature-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const action = this.dataset.action;
                    handleFeatureAction(action);
                });
            });
        }

        function sendMessage() {
            const chatInput = document.getElementById('chat-input');
            const message = chatInput.value.trim();
            
            if (message) {
                addUserMessage(message);
                chatInput.value = '';
                document.getElementById('send-btn').disabled = true;
                handleUserMessage(message, true);
            }
        }

        function addUserMessage(message) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message user-message';
            messageDiv.innerHTML = `
                <div class="message-content">
                    <p>${message}</p>
                </div>
                <div class="message-avatar">👤</div>
            `;
            chatMessages.appendChild(messageDiv);
            scrollToBottom();
        }

        function addBotMessage(message, delay = 1000) {
            showTypingIndicator();
            
            setTimeout(() => {
                hideTypingIndicator();
                
                const chatMessages = document.getElementById('chat-messages');
                const messageDiv = document.createElement('div');
                messageDiv.className = 'message bot-message';
                messageDiv.innerHTML = `
                    <div class="message-avatar">🤖</div>
                    <div class="message-content">
                        <p>${message}</p>
                    </div>
                `;
                chatMessages.appendChild(messageDiv);
                scrollToBottom();
                
                // Award XP for chatting
                addXP(XP_VALUES.chatBot);
            }, delay);
        }

        function showTypingIndicator() {
            if (isTyping) return;
            
            isTyping = true;
            document.getElementById('bot-status').textContent = 'Typing...';
            
            const chatMessages = document.getElementById('chat-messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'message bot-message typing-message';
            typingDiv.id = 'typing-indicator';
            typingDiv.innerHTML = `
                <div class="message-avatar">🤖</div>
                <div class="message-content">
                    <div class="typing-indicator">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            `;
            chatMessages.appendChild(typingDiv);
            scrollToBottom();
        }

        function hideTypingIndicator() {
            isTyping = false;
            document.getElementById('bot-status').textContent = 'Online';
            
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        function scrollToBottom() {
            const chatMessages = document.getElementById('chat-messages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function handleUserMessage(message, true) {
            const lowerMessage = message.toLowerCase();
            let response = '';

            // Simple keyword-based responses
            if (lowerMessage.includes('hello') || lowerMessage.includes('hi')) {
                response = "Hello! It's wonderful to connect with you. How are you feeling today?";
            } else if (lowerMessage.includes('sad') || lowerMessage.includes('down') || lowerMessage.includes('depressed')) {
                response = "I hear that you're feeling sad, and I want you to know that it's okay to feel this way. Difficult emotions are part of the human experience. Have you tried any breathing exercises today? Sometimes taking a few deep breaths can help create a small shift in how we feel.";
            } else if (lowerMessage.includes('anxious') || lowerMessage.includes('worried') || lowerMessage.includes('stressed')) {
                response = "Anxiety can feel overwhelming, but you're not alone in this. One technique that many find helpful is the 4-7-8 breathing method: breathe in for 4 counts, hold for 7, and exhale for 8. Would you like me to guide you through a breathing exercise?";
            } else if (lowerMessage.includes('happy') || lowerMessage.includes('good') || lowerMessage.includes('great')) {
                response = "That's wonderful to hear! I'm so glad you're feeling positive. These good moments are worth celebrating. What brought you joy today?";
            } else if (lowerMessage.includes('motivation') || lowerMessage.includes('motivate')) {
                response = getRandomQuote() + " Remember, every small step you take matters. You're stronger than you think!";
            } else if (lowerMessage.includes('tip') || lowerMessage.includes('advice')) {
                response = getWellnessTip();
            } else if (lowerMessage.includes('progress') || lowerMessage.includes('achievement')) {
                const userData = getUserData();
                response = `Let's celebrate your progress! You're on level ${userData.level} with ${userData.xp} XP, and you've earned ${userData.badges.length} badges. Your ${userData.streak}-day streak shows real commitment to your wellness journey!`;
            } else if (lowerMessage.includes('breathing') || lowerMessage.includes('breathe')) {
                response = "Breathing exercises are a wonderful way to center yourself. Would you like to try the breathing exercise in the Tasks section? I can guide you through it, or you can practice on your own.";
            } else if (lowerMessage.includes('journal') || lowerMessage.includes('write')) {
                response = "Journaling is such a powerful tool for self-reflection. It helps you process emotions and gain clarity. Have you tried today's journal prompt? Writing even just a few sentences can make a difference.";
            } else if (lowerMessage.includes('thank')) {
                response = "You're so welcome! It's my pleasure to support you on this journey. Remember, taking care of your mental health is one of the most important things you can do for yourself.";
            } else {
                // Default responses
                const defaultResponses = [
                    "I appreciate you sharing that with me. How are you feeling about it?",
                    "That sounds important to you. Would you like to talk more about it?",
                    "I'm here to listen and support you. What would be most helpful right now?",
                    "Thank you for opening up. Your feelings are valid and important.",
                    "It takes courage to express yourself. How can I best support you today?"
                ];
                response = defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
            }

            addBotMessage(response);
        }

        function handleFeatureAction(action) {
            switch (action) {
                case 'motivation':
                    addBotMessage(getRandomQuote() + " You've got this! Every day you choose to prioritize your mental health is a victory worth celebrating.");
                    break;
                case 'tips':
                    addBotMessage(getWellnessTip());
                    break;
                case 'breathing':
                    addBotMessage("Let's do a quick breathing exercise together! Find a comfortable position and follow along: Breathe in slowly for 4 counts... hold for 4... and breathe out for 6. Let's repeat this 3 times. Focus only on your breath.");
                    break;
                case 'progress':
                    const userData = getUserData();
                    if (userData) {
                        addBotMessage(`You're doing amazing! Here's your progress: Level ${userData.level}, ${userData.xp} XP earned, ${userData.streak} day streak, and ${userData.badges.length} badges unlocked. You've completed ${userData.stats.totalBreathingSessions} breathing sessions and written ${userData.stats.totalJournalEntries} journal entries. Keep up the fantastic work!`);
                    }
                    break;
            }
        }

        function getWellnessTip() {
            const tips = [
                "Try the 5-4-3-2-1 grounding technique: Name 5 things you can see, 4 you can touch, 3 you can hear, 2 you can smell, and 1 you can taste.",
                "Practice gratitude by writing down 3 things you're thankful for each day. It can shift your focus to the positive.",
                "Take regular breaks from screens. Even 5 minutes looking out a window can refresh your mind.",
                "Stay hydrated! Dehydration can affect your mood and energy levels more than you might think.",
                "Try progressive muscle relaxation: tense and then relax each muscle group in your body, starting from your toes.",
                "Establish a consistent sleep schedule. Good sleep is fundamental to mental health.",
                "Connect with nature, even if it's just stepping outside for a few minutes or looking at plants.",
                "Practice saying 'no' to commitments that drain your energy. Protecting your time is protecting your mental health."
            ];
            return tips[Math.floor(Math.random() * tips.length)];
        }
    </script>

    <style>
        .bot-content {
            max-width: 1000px;
            margin: 0 auto;
        }

        .chat-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 0;
            margin-bottom: 2rem;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .bot-avatar-section {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .bot-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .bot-info h3 {
            margin: 0;
            font-size: 1.3rem;
        }

        .bot-status {
            margin: 0;
            opacity: 0.8;
            font-size: 0.9rem;
        }

        .chat-messages {
            height: 400px;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            display: flex;
            gap: 0.75rem;
            max-width: 80%;
        }

        .user-message {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .bot-message {
            align-self: flex-start;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            flex-shrink: 0;
        }

        .user-message .message-avatar {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .bot-message .message-avatar {
            background: rgba(102, 126, 234, 0.1);
        }

        .message-content {
            background: #f8f9fa;
            padding: 0.75rem 1rem;
            border-radius: 15px;
            position: relative;
        }

        .user-message .message-content {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .message-content p {
            margin: 0;
            line-height: 1.4;
        }

        .quick-responses {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            flex-wrap: wrap;
            border-top: 1px solid #eee;
        }

        .quick-response-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #667eea;
            background: transparent;
            color: #667eea;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }

        .quick-response-btn:hover {
            background: #667eea;
            color: white;
        }

        .chat-input-container {
            display: flex;
            gap: 0.5rem;
            padding: 1rem;
            border-top: 1px solid #eee;
        }

        #chat-input {
            flex: 1;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 25px;
            font-size: 1rem;
            outline: none;
        }

        #chat-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .bot-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card h3 {
            margin: 0 0 1rem 0;
            color: #333;
        }

        .feature-card p {
            margin: 0 0 1.5rem 0;
            color: #666;
        }

        .feature-btn {
            padding: 0.75rem 1.5rem;
            background: linear-gradient(45deg, #4facfe, #00f2fe);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .feature-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(79, 172, 254, 0.4);
        }

        .typing-message .message-content {
            background: rgba(102, 126, 234, 0.1);
        }

        /* Custom scrollbar for chat */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }

        .chat-messages::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .chat-messages::-webkit-scrollbar-thumb {
            background: #667eea;
            border-radius: 3px;
        }

        @media (max-width: 768px) {
            .chat-messages {
                height: 300px;
            }
            
            .message {
                max-width: 90%;
            }
            
            .quick-responses {
                flex-direction: column;
            }
            
            .quick-response-btn {
                text-align: center;
            }
            
            .bot-features {
                grid-template-columns: 1fr;
            }
        }
    </style>
    <script src="js/enhanced-features.js"></script>
    <script src="js/advanced-ai.js"></script>
</body>
</html>
