<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MindJourney - Your Mental Wellness Companion</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="welcome-page">
    <div class="container">
        <header class="welcome-header">
            <h1 class="app-title">MindJourney</h1>
            <p class="app-subtitle">Your Personal Mental Wellness Adventure</p>
        </header>

        <main class="welcome-content">
            <!-- First Time User Setup -->
            <div id="setup-screen" class="setup-screen">
                <div class="setup-card">
                    <h2>Welcome to Your Journey</h2>
                    <p>Let's start by creating your personal companion</p>
                    
                    <div class="input-group">
                        <label for="username">What should we call you?</label>
                        <input type="text" id="username" placeholder="Enter your name" maxlength="20">
                    </div>

                    <div class="avatar-selection">
                        <h3>Choose Your Avatar</h3>
                        <div class="avatar-grid">
                            <div class="avatar-option" data-avatar="peaceful">
                                <div class="avatar-circle peaceful-avatar">🧘</div>
                                <span>Peaceful</span>
                            </div>
                            <div class="avatar-option" data-avatar="nature">
                                <div class="avatar-circle nature-avatar">🌱</div>
                                <span>Nature</span>
                            </div>
                            <div class="avatar-option" data-avatar="strong">
                                <div class="avatar-circle strong-avatar">💪</div>
                                <span>Strong</span>
                            </div>
                            <div class="avatar-option" data-avatar="wise">
                                <div class="avatar-circle wise-avatar">🦉</div>
                                <span>Wise</span>
                            </div>
                            <div class="avatar-option" data-avatar="creative">
                                <div class="avatar-circle creative-avatar">🎨</div>
                                <span>Creative</span>
                            </div>
                            <div class="avatar-option" data-avatar="bright">
                                <div class="avatar-circle bright-avatar">⭐</div>
                                <span>Bright</span>
                            </div>
                        </div>
                    </div>

                    <button id="start-journey" class="primary-btn" disabled>Begin My Journey</button>
                </div>
            </div>

            <!-- Returning User Welcome -->
            <div id="returning-user" class="returning-user hidden">
                <div class="welcome-back-card">
                    <div class="user-avatar-display">
                        <div id="user-avatar" class="avatar-circle"></div>
                    </div>
                    <h2>Welcome back, <span id="display-username"></span>!</h2>
                    <div class="quick-stats">
                        <div class="stat-item">
                            <span class="stat-label">Level</span>
                            <span id="user-level" class="stat-value">1</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">XP</span>
                            <span id="user-xp" class="stat-value">0</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Streak</span>
                            <span id="user-streak" class="stat-value">0</span>
                        </div>
                    </div>
                    <button id="continue-journey" class="primary-btn">Continue Journey</button>
                    <button id="reset-progress" class="secondary-btn">Start Fresh</button>
                </div>
            </div>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script>
        // Initialize welcome page
        document.addEventListener('DOMContentLoaded', function() {
            // Always redirect to sign in page first for proper authentication flow
            window.location.href = 'signin.html';
        });

        function showSetupScreen() {
            document.getElementById('setup-screen').classList.remove('hidden');
            document.getElementById('returning-user').classList.add('hidden');
        }

        function showReturningUser(userData) {
            document.getElementById('setup-screen').classList.add('hidden');
            document.getElementById('returning-user').classList.remove('hidden');
            
            document.getElementById('display-username').textContent = userData.username;
            document.getElementById('user-level').textContent = userData.level || 1;
            document.getElementById('user-xp').textContent = userData.xp || 0;
            document.getElementById('user-streak').textContent = userData.streak || 0;
            
            const avatarElement = document.getElementById('user-avatar');
            avatarElement.className = `avatar-circle ${userData.avatar}-avatar`;
            avatarElement.textContent = getAvatarEmoji(userData.avatar);
        }

        // Avatar selection logic
        document.querySelectorAll('.avatar-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.avatar-option').forEach(opt => opt.classList.remove('selected'));
                this.classList.add('selected');
                document.getElementById('start-journey').disabled = false;
            });
        });

        // Username input validation
        document.getElementById('username').addEventListener('input', function() {
            const startBtn = document.getElementById('start-journey');
            const selectedAvatar = document.querySelector('.avatar-option.selected');
            startBtn.disabled = !(this.value.trim() && selectedAvatar);
        });

        // Start journey button
        document.getElementById('start-journey').addEventListener('click', function() {
            const username = document.getElementById('username').value.trim();
            const selectedAvatar = document.querySelector('.avatar-option.selected');
            
            if (username && selectedAvatar) {
                const avatarType = selectedAvatar.dataset.avatar;
                initializeUser(username, avatarType);
                window.location.href = 'dashboard.html';
            }
        });

        // Continue journey button
        document.getElementById('continue-journey').addEventListener('click', function() {
            window.location.href = 'dashboard.html';
        });

        // Reset progress button
        document.getElementById('reset-progress').addEventListener('click', function() {
            if (confirm('Are you sure you want to start fresh? This will delete all your progress.')) {
                clearUserData();
                showSetupScreen();
            }
        });

        function getAvatarEmoji(avatarType) {
            const avatars = {
                peaceful: '🧘',
                nature: '🌱',
                strong: '💪',
                wise: '🦉',
                creative: '🎨',
                bright: '⭐'
            };
            return avatars[avatarType] || '🧘';
        }
    </script>
    <script src="js/enhanced-features.js"></script>
</body>
</html>
