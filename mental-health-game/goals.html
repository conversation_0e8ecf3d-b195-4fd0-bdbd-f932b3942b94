<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Goals - MindJourney</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/animations.css">
</head>
<body class="goals-page">
    <div class="container">
        <header class="page-header">
            <h1>🎯 Wellness Goals</h1>
            <p>Set and track your mental wellness objectives</p>
            <nav class="main-nav">
                <a href="dashboard.html" class="nav-link">Dashboard</a>
                <a href="tasks.html" class="nav-link">Tasks</a>
                <a href="rewards.html" class="nav-link">Rewards</a>
                <a href="bot.html" class="nav-link">AI Companion</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link">Goals</a>
                <a href="ai-insights.html" class="nav-link">AI Insights</a>
                <a href="sleep-tracker.html" class="nav-link">Sleep</a>
                <a href="nutrition-tracker.html" class="nav-link">Nutrition</a>
                <a href="analytics.html" class="nav-link">Analytics</a>
                <a href="goals.html" class="nav-link active">Goals</a>
                <a href="settings.html" class="nav-link">Settings</a>
            </nav>
        </header>

        <main class="goals-content">
            <!-- Active Goals -->
            <section class="goals-section">
                <div class="dashboard-card">
                    <h2>🎯 Active Goals</h2>
                    <div id="active-goals" class="goals-list">
                        <div class="no-goals">
                            <p>No active goals yet. Create your first wellness goal below!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Create New Goal -->
            <section class="goals-section">
                <div class="dashboard-card">
                    <h2>➕ Create New Goal</h2>
                    
                    <div class="goal-creation">
                        <div class="goal-type-selector">
                            <h3>Choose Goal Type:</h3>
                            <div class="goal-types">
                                <div class="goal-type-card" data-type="streak">
                                    <div class="goal-icon">🔥</div>
                                    <h4>Streak Goal</h4>
                                    <p>Maintain daily consistency</p>
                                </div>
                                <div class="goal-type-card" data-type="activity">
                                    <div class="goal-icon">📊</div>
                                    <h4>Activity Goal</h4>
                                    <p>Complete specific activities</p>
                                </div>
                                <div class="goal-type-card" data-type="mood">
                                    <div class="goal-icon">😊</div>
                                    <h4>Mood Goal</h4>
                                    <p>Improve emotional wellbeing</p>
                                </div>
                                <div class="goal-type-card" data-type="custom">
                                    <div class="goal-icon">⭐</div>
                                    <h4>Custom Goal</h4>
                                    <p>Create your own objective</p>
                                </div>
                            </div>
                        </div>

                        <div class="goal-form hidden" id="goal-form">
                            <div class="input-group">
                                <label for="goal-title">Goal Title:</label>
                                <input type="text" id="goal-title" placeholder="Enter your goal title" maxlength="50">
                            </div>

                            <div class="input-group">
                                <label for="goal-description">Description:</label>
                                <textarea id="goal-description" placeholder="Describe your goal in detail" maxlength="200"></textarea>
                            </div>

                            <div class="goal-parameters" id="goal-parameters">
                                <!-- Dynamic content based on goal type -->
                            </div>

                            <div class="input-group">
                                <label for="goal-deadline">Target Date:</label>
                                <input type="date" id="goal-deadline">
                            </div>

                            <div class="goal-actions">
                                <button id="create-goal" class="primary-btn">Create Goal</button>
                                <button id="cancel-goal" class="secondary-btn">Cancel</button>
                                <button id="ai-suggest-goal" class="secondary-btn">🤖 AI Suggest</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Completed Goals -->
            <section class="goals-section">
                <div class="dashboard-card">
                    <h2>✅ Completed Goals</h2>
                    <div id="completed-goals" class="goals-list">
                        <div class="no-goals">
                            <p>Complete your first goal to see it here!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Goal Templates -->
            <section class="goals-section">
                <div class="dashboard-card">
                    <h2>📋 Goal Templates</h2>
                    <p>Quick start with these proven wellness goals</p>
                    
                    <div class="goal-templates">
                        <div class="template-card" data-template="7-day-streak">
                            <h4>🔥 7-Day Consistency</h4>
                            <p>Complete all daily tasks for 7 days straight</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                        
                        <div class="template-card" data-template="mood-improvement">
                            <h4>😊 Mood Boost</h4>
                            <p>Achieve average mood of "Happy" for one week</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                        
                        <div class="template-card" data-template="meditation-master">
                            <h4>🧘 Meditation Master</h4>
                            <p>Complete 60 minutes of meditation this month</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                        
                        <div class="template-card" data-template="journal-journey">
                            <h4>📝 Journal Journey</h4>
                            <p>Write 10 meaningful journal entries</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                        
                        <div class="template-card" data-template="breathing-basics">
                            <h4>🌬️ Breathing Basics</h4>
                            <p>Complete 20 breathing exercises</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                        
                        <div class="template-card" data-template="wellness-warrior">
                            <h4>⚔️ Wellness Warrior</h4>
                            <p>Reach level 5 in your wellness journey</p>
                            <button class="template-btn">Use Template</button>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <div class="floating-elements">
            <div class="floating-circle circle-1"></div>
            <div class="floating-circle circle-2"></div>
            <div class="floating-circle circle-3"></div>
        </div>
    </div>

    <script src="js/API_key.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/app.js"></script>
    <script src="js/openai-integration.js"></script>
    <script src="js/enhanced-features.js"></script>
    <script src="js/goals.js"></script>

    <style>
        .goals-content {
            max-width: 1000px;
            margin: 0 auto;
        }

        .goals-section {
            margin-bottom: 2rem;
        }

        .goals-list {
            margin: 1.5rem 0;
        }

        .no-goals {
            text-align: center;
            padding: 2rem;
            color: #666;
            font-style: italic;
        }

        .goal-card {
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #667eea;
            transition: all 0.3s ease;
        }

        .goal-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .goal-card.completed {
            border-left-color: #56ab2f;
            background: rgba(86, 171, 47, 0.1);
        }

        .goal-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }

        .goal-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .goal-status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .goal-status.active {
            background: #667eea;
            color: white;
        }

        .goal-status.completed {
            background: #56ab2f;
            color: white;
        }

        .goal-description {
            color: #666;
            margin-bottom: 1rem;
            line-height: 1.5;
        }

        .goal-progress {
            margin-bottom: 1rem;
        }

        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
        }

        .progress-percentage {
            font-weight: 600;
            color: #667eea;
        }

        .goal-actions {
            display: flex;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .goal-action-btn {
            padding: 0.5rem 1rem;
            border: none;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .complete-btn {
            background: #56ab2f;
            color: white;
        }

        .edit-btn {
            background: #ffc107;
            color: white;
        }

        .delete-btn {
            background: #ff6b6b;
            color: white;
        }

        .goal-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .goal-type-card {
            text-align: center;
            padding: 1.5rem;
            border: 2px solid transparent;
            border-radius: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(102, 126, 234, 0.05);
        }

        .goal-type-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .goal-type-card.selected {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .goal-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .goal-type-card h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .goal-type-card p {
            margin: 0;
            color: #666;
            font-size: 0.9rem;
        }

        .goal-parameters {
            margin: 1rem 0;
        }

        .parameter-group {
            margin-bottom: 1rem;
        }

        .parameter-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #555;
        }

        .parameter-group input,
        .parameter-group select {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 1rem;
        }

        .goal-templates {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin: 1.5rem 0;
        }

        .template-card {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            border: 2px solid rgba(102, 126, 234, 0.1);
            text-align: center;
            transition: all 0.3s ease;
        }

        .template-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
        }

        .template-card h4 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }

        .template-card p {
            margin: 0 0 1rem 0;
            color: #666;
            font-size: 0.9rem;
        }

        .template-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .template-btn:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .goal-types {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .goal-templates {
                grid-template-columns: 1fr;
            }
            
            .goal-header {
                flex-direction: column;
                gap: 0.5rem;
            }
            
            .goal-actions {
                justify-content: center;
            }
        }
    </style>
</body>
</html>
